import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Icon } from '@iconify/react';
import { getAssessmentDetails, getAssessmentAnalyticsUsingGemini } from '../../../services/adminService';
import { decodeData } from '../../../utils/encodeAndEncode';

function CourseAnalyticsAssessmentDetails() {
  const { assessmentId, courseId } = useParams();
  const decodedCourseId = courseId ? decodeData(courseId) : null;
  const decodedAssessmentId = assessmentId ? decodeData(assessmentId) : null;
  const navigate = useNavigate();

  const [assessmentDetails, setAssessmentDetails] = useState(null);
  const [recentAttempts, setRecentAttempts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false);

  useEffect(() => {
    fetchAssessmentDetails();
  }, [decodedAssessmentId, decodedCourseId]);

  const fetchAssessmentDetails = async () => {
    try {
      setLoading(true);
        const response = await getAssessmentDetails(decodedAssessmentId, decodedCourseId);

      console.log('Assessment Details Response:', response);

      if (response.success && response.data) {
        setAssessmentDetails(response.data.assessment);
        setRecentAttempts(response.data.recent_attempts || []);
      }
    } catch (error) {
      console.error('Error fetching assessment details:', error);
    } finally {
      setLoading(false);
    } 

  };

  const handleGetAnalytics = async () => {
    try {
      setAnalyticsLoading(true);
      const payload = {
        assessment_id: decodedAssessmentId
      };
      
      const response = await getAssessmentAnalyticsUsingGemini(payload);
      console.log('Assessment Analytics Response:', response);
      
      if (response.success) {
        setAnalyticsData(response);
        setShowAnalyticsModal(true);
      }
      
    } catch (error) {
      console.error('Error fetching assessment analytics:', error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!assessmentDetails) {
    return (
      <div className="text-center py-5">
        <div className="text-muted">Assessment not found</div>
        <button className="btn btn-primary mt-3" onClick={() => navigate(-1)}>
          Go Back
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header with Get Analytics Button */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">Assessment Analytics</h4>
            <button 
              className="btn btn-primary d-flex align-items-center gap-2 w-auto"
              onClick={handleGetAnalytics}
              disabled={analyticsLoading}
            >
              {analyticsLoading ? (
                <>
                  <div className="spinner-border spinner-border-sm" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <span>Getting Analytics...</span>
                </>
              ) : (
                <>
                  <Icon icon="mdi:chart-line" width="18" />
                  <span>Get Analytics</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Assessment Overview */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Questions</div>
                    <div className="h5 mb-0">{assessmentDetails.total_questions || 0}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Total Attempts</div>
                    <div className="h5 mb-0">{assessmentDetails.total_attempts || 0}</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Average Score</div>
                    <div className="h5 mb-0">{assessmentDetails.average_score || 0}%</div>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className="border rounded p-3">
                    <div className="text-muted mb-1">Pass Rate</div>
                    <div className="h5 mb-0">{assessmentDetails.pass_rate || 0}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assessment Information */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="card h-100">
            <div className="card-body">
              <h5 className="card-title mb-3">Assessment Information</h5>
              <div className="row g-3">
                <div className="col-6">
                  <div className="text-muted">Duration</div>
                  <div className="fw-medium">{assessmentDetails.duration ? `${assessmentDetails.duration} minutes` : 'Not specified'}</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Pass Percentage</div>
                  <div className="fw-medium">{assessmentDetails.pass_percentage || 0}%</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Points</div>
                  <div className="fw-medium">{assessmentDetails.earn_point || 0}</div>
                </div>
                <div className="col-6">
                  <div className="text-muted">Status</div>
                  <div className="fw-medium">
  <span className="badge text-primary bg-light border border-primary">
    {assessmentDetails.is_active ? 'Active' : 'Inactive'}
  </span>
</div>

                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card h-100">
            <div className="card-body">
              <h5 className="card-title mb-3">Course Information</h5>
              <div className="text-muted">Assessment Name</div>
              <div className="fw-medium">{assessmentDetails.assessment_name || 'N/A'}</div>
              <div className="text-muted mt-3">Course Name</div>
              <div className="fw-medium">{assessmentDetails.course_name || 'N/A'}</div>
              <div className="text-muted mt-3">Created Date</div>
              <div className="fw-medium">
                {assessmentDetails.createdAt
                  ? new Date(assessmentDetails.createdAt).toLocaleDateString()
                  : 'N/A'
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Attempts */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-4">Recent Attempts</h5>
              {recentAttempts.length === 0 ? (
                <div className="text-center py-4 text-muted">
                  No attempts found for this assessment
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-borderless mb-0">
                    <thead>
                      <tr>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>User</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Score</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Status</th>
                        <th style={{ backgroundColor: '#f8f9fa', fontWeight: '500' }}>Last Attempt</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentAttempts.map((attempt, index) => (
                        <tr key={index} className="border-bottom">
                          <td className="py-3 align-middle">
                            <div className="d-flex align-items-center gap-2">
                              <div className="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                   style={{ width: '32px', height: '32px', fontSize: '14px', color: 'white' }}>
                                {attempt.user_name?.charAt(0)?.toUpperCase() || 'U'}
                              </div>
                              <div>
                                <div className="fw-medium">{attempt.user_name || 'Unknown User'}</div>
                                <div className="text-muted small">{attempt.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="fw-medium">{attempt.score_percentage}%</div>
                            <div className="text-muted small">
                              {attempt.correct_answers}/{attempt.total_questions} correct
                            </div>
                          </td>
                          <td className="py-3 align-middle">
                            <span className={`badge ${attempt.passed ? 'bg-success' : 'bg-danger'}`}>
                              {attempt.passed ? 'Passed' : 'Failed'}
                            </span>
                          </td>
                          <td className="py-3 align-middle">
                            <div className="text-muted">
                              {attempt.last_attempt_date
                                ? new Date(attempt.last_attempt_date).toLocaleDateString()
                                : 'N/A'
                              }
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Modal */}
      {showAnalyticsModal && analyticsData && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered modal-xl">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title d-flex align-items-center">
                  <Icon icon="mdi:chart-line" className="me-2" width="24" height="24" />
                  Assessment Analytics
                  {!analyticsData.geminiAvailable && (
                    <span className="badge bg-warning ms-2">Fallback Analysis</span>
                  )}
                </h5>
                <button 
                  type="button" 
                  className="btn-close" 
                  onClick={() => setShowAnalyticsModal(false)}
                  aria-label="Close"
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-12">
                    <div className="alert alert-info">
                      <div className="d-flex align-items-start">
                        <Icon icon="mdi:information" className="me-2 mt-1" width="18" />
                        <div>
                          <strong>Assessment:</strong> {assessmentDetails?.assessment_name}
                          <br />
                          <small className="text-muted">
                            {analyticsData.geminiAvailable 
                              ? 'AI-powered analysis using Gemini' 
                              : 'Fallback analysis (Gemini API unavailable)'
                            }
                          </small>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card">
                      <div className="card-body">
                        <h6 className="card-title mb-3">Analysis Results</h6>
                        <div 
                          style={{ 
                            maxHeight: '500px', 
                            overflowY: 'auto',
                            whiteSpace: 'pre-wrap',
                            fontFamily: 'monospace',
                            fontSize: '14px',
                            lineHeight: '1.6'
                          }}
                        >
                          {analyticsData.analysis}
                        </div>
                      </div>
                    </div>

                    {analyticsData.stats && (
                      <div className="card mt-3">
                        <div className="card-body">
                          <h6 className="card-title mb-3">Question Statistics</h6>
                          <div className="table-responsive">
                            <table className="table table-sm">
                              <thead>
                                <tr>
                                  <th>Question ID</th>
                                  <th>Total Attempts</th>
                                  <th>Correct</th>
                                  <th>Wrong</th>
                                  <th>Success Rate</th>
                                </tr>
                              </thead>
                              <tbody>
                                {Object.entries(analyticsData.stats).map(([questionId, stats]) => (
                                  <tr key={questionId}>
                                    <td>Q{questionId}</td>
                                    <td>{stats.total}</td>
                                    <td className="text-success">{stats.correct}</td>
                                    <td className="text-danger">{stats.wrong}</td>
                                    <td>
                                      <span className={`badge ${
                                        stats.total > 0 
                                          ? (stats.correct / stats.total) >= 0.8 
                                            ? 'bg-success' 
                                            : (stats.correct / stats.total) >= 0.6 
                                              ? 'bg-warning' 
                                              : 'bg-danger'
                                          : 'bg-secondary'
                                      }`}>
                                        {stats.total > 0 ? `${((stats.correct / stats.total) * 100).toFixed(1)}%` : 'N/A'}
                                      </span>
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={() => setShowAnalyticsModal(false)}
                >
                  <Icon icon="mdi:close" className="me-1" width="16" />
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default CourseAnalyticsAssessmentDetails;
