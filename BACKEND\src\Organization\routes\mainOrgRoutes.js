const router = require("express").Router();
const jwt = require("jsonwebtoken");
const multer = require("multer");
// comment
const {
  getDashBoardStats,
  getUserQueries,
  getOrgPerformanceStatistics,
  getUsersMonthlyWise,
  getCreatedCourseAndClassroomMonthlyWise,
  getTopTrainers,
} = require("../organisationController/dashboard/dashboard");
const {
  authenticateJWT,
  registerValidation,
} = require("../../middleware/Validator");
const {
  getProfile,
  organizationRoles,
  editOrganizationRoles,
  updatePasswordAPI,
} = require("../controller/accounts/orgUser");
const upload = multer({ storage: multer.memoryStorage() });
const uploadVideo = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 150 * 1024 * 1024 }, // 150MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith("video/")) {
      cb(null, true);
    } else {
      cb(new Error("Only video files are allowed!"));
    }
  },
});

const uploadNew = uploadVideo.fields([
  { name: "video", maxCount: 1 },
  { name: "attachment_file", maxCount: 1 },
]);

const { updateUserProfile } = require("../controller/accounts/orgUser");
const { getUserLogs, getDashboardData } = require("../controller/dashboard/dashboard");
const {
  getAllCourses,
  getAllCoursesAPI,
  createAssignment,
  deleteModule,
  deleteAssignment,
  createAssessment,
  addMCQOptions,
  addCorrectAnswer,
  createQuestion,
  deleteMCQOption,
  deleteAssessment,
  updateAssignment,
  getAssignmentsByModule,
  getAssessmentsByModule,
  deactivateModule,
  deleteCourse,
  createCourseApproval,
  approveCourseApproval,
  deleteVideoDetails,
  getVideoDetails,
  updateVideoDetails,
  updateAssessment,
  changeAssessmentStatus,
  editModule,
  changeAssignmentStatus,
  getQuestionsForAssessment,
  deleteQuestion,
  editQuestion,
  getUserAssessmentResults,
  userActivity,
  getUserAllResults,
  getAssignmentResults,
  getSurvyResults,
  getUserCourseList,
  getPerformanceSummary,
  deleteModuleContent,
  downloadTraineeData,
  moduleActiveDeactive,
  createDocument,
  ModuleContentStatus,
  EditDocument,
  getDocumentById,
  addQuestion,
  editVideoContent,
  getQuestions,
  getCourseVideos,
  getVideoDetailsById,
} = require("../organisationController/courses/course");
const { authorizePermissions } = require("../../middleware/Validator");
const {
  createCourse,
  createCourseModule,
  addVideoAndDetails,
  updateCourse,
  getModulesList,
  getModuleContent,
  updateContentOrder,
  bulkUploadVideos,
  getCoursePassFailStats,
  getUserAssessmentDetails,
  CourseUserActivity,
  getSurveysByCourseId,
  getSingleSurveyDetails,
  getAssessmentsByCourseId,
  getAssessmentDetails,
  singleCourseDetails,
  getCourseCategories,
  getCourseApprovalANDApprovedANDRejected,
  courseApproving,
  courseReject,
  getCourseDetailsByIdForNotification,
  checkCoursePurchase,
  getCourseDetailsByID,
  getAssessmentAnalyticsUsingGemini,
} = require("../controller/courses/courses");
const {
  getAllClasses,
  getAllStudentApproval,
  classroomDashboard,
  changeTheVisibilityOfTabs,

  deleteClassRoom,
  createClassroom,
  getAllTrainers,
  updateClassroom,
  createLiveClass,
  getLiveClasses,
  CreateZoomLive,
  approveClassroom,
  updateLiveClass,
  deleteLiveClass,
  resendLiveClassMail,
  activateLiveClass,
  deactivateLiveClass,
  approveOrDenyClassroom,
  activeDeactiveClassroom,
  logEndLiveClass,
} = require("../organisationController/classroom/classroom");
const {
  getOrgProfileDetails,
  deleteOrgProfileDetails,
  getTraineeDetails,
  updateTraineeProfile,
  bulkUploadUsers,
  downloadSampleTemplate,
  TraineeAnalytics,
} = require("../organisationController/accounts/account");
const {
  approveCourse,
  rejectCourse,
  getAllPendingCourses,
  getParticularUserDetails,
  getApprovedCourses,
  sendApprovalcourse,
  cancelApprovalCourseRequest,
} = require("../organisationController/courseApproval/courseApproval");
const {
  getUserDetails,
  createOrganistionUser,
  getTraineeDataWithCertificateCount,
  DeactiveUser,
  getTrainerDetails,
  activeDeactiveUser,
  editOrganisationUser,
  createAdminUser,
} = require("../organisationController/users/users");
const {
  activateRole,
  deactivateRole,
  getRolesAndPermissions,
  deleteRole,
  createRoleWithPermissions,
  getAllPermissions,
  getRolePermissionsById,
  updateRoleById,
  createGetFullModuleHierarchyWithActions,
  getPermissionsByRoleId,

  getFullModuleHierarchyWithActions,
  updateRolePermission,
  insertRoleAndPermissions,
  deleteRoleAndPermissions
} = require("../organisationController/rolesAndPermissions/rolesAndPermissions");
const {
  addCertificateTemplate,
  deleteCertificateTemplate,
  updateCertificateTemplate,
  getCertificateTemplates,
  changeCertificateStatus,
} = require("../organisationController/certificates/certificate");
const {
  updateNotificationSettings,
  getNotificationSettings,
} = require("../controller/settings/notifications_settings");
const {
  addAboutOrganization,
  getAboutOrganization,
  editAboutOrganization,
  statusAboutOrganization,
  deleteAboutOrganization,
} = require("../organisationController/Settings/AboutOrganization.js");
const {
  getFAQs,
  addFAQ,
  updateFAQ,
  statusFAQ,
  deleteFAQ,
} = require("../organisationController/Settings/FAQ.js");
const {
  getPayments,
  getPaymentById,
} = require("../organisationController/Settings/payment.js");
const {
  getTickets,
  sendTicketResponse,
} = require("../organisationController/Settings/tickets.js");
const {
  createAnnouncement,
  getAnnouncements,
  updateAnnouncement,
  deleteAnnouncement,
  getAnnouncementsRoles,
} = require("../organisationController/announcements/announcement");
const {
  createCertificatesForRandomUsers,
  createCertificate,
  getTemplates,
  editTemplates,
  deleteTemplate,
  getCertificates,
} = require("../controller/certificates/certificate");
const {
  getAllTraineesForClassroom,
  addTraineeToClassroom,
  removeTraineeFromClassroom,
  getAllUsersExceptClassroom,
  deleteTraineeFromClassroom,
} = require("../organisationController/classroom/classroomUsers");
const {
  getClassroomAssessments,
  addClassroomAssessment,
  updateClassroomAssessment,
  deleteClassroomAssessment,
  addQuestionsToAssessment,
  removeQuestionFromAssessment,
  getTraineeAssessmentResults,
  getAssessmentResultsforTrainee,
  getAllTraineeAssessments,
  getTraineeClassroomAssessments,
  getAssessmentQuestions,
  getAvailableQuestionsForAssessment,
  getAssessmentAnalytics,
  copyClassroomAssessment,
} = require("../organisationController/classroom/classroomAssessments");

const {
  getQuestionTags,
  qbCreateQuestion,
  qbUpdateQuestion,
  qbDeleteQuestion,
  qbGetQuestions,
  qbGetQuestionById,
  deleteQuestionTag,
} = require("../organisationController/questionBank/questions");
const {
  getClassroomDashboardDetails,
  getTraineeClassrooms,
  getQuestionsForTraineeAssessment,
  submitTraineeAssessment,
  getTraineeAssessments,
} = require("../organisationController/classroom/userClassroomAssessment");
const {
  createClassroomAssignment,
  deleteClassroomAssignment,
  getClassroomAssignments,
  getClassroomAssignmentById,
  updateClassroomAssignment,
  getClassroomAssignmentSubmissions,
  userAssignmentStatus,
  gradeClassroomAssignment,
  addClassroomAssignmentQuestion,
  updateClassroomAssignmentQuestion,
  deleteClassroomAssignmentQuestion,
  getClassroomAssignmentQuestions,
  getUserClassroomAssignmentsQuestions,
  submitUserClassroomAssignmentResponse,
} = require("../organisationController/classroom/classroomAssignments");

// Import new Zoom Meeting SDK Live Class Controller
const {
  createLiveClass: createZoomLiveClass,
  getLiveClasses: getZoomLiveClasses,
  updateLiveClass: updateZoomLiveClass,
  deleteLiveClass: deleteZoomLiveClass,
  startLiveClass,
  endLiveClass,
  generateJoinToken,
  debugMeeting,
} = require("../organisationController/classroom/classroomLiveClass");

const { route } = require("./organizationroutes");
// const { WriteGetObjectResponseRequestFilterSensitiveLog } = require('@aws-sdk/client-s3');






const {
  createClassroomResource,
  getClassroomResources,
  updateClassroomResource,
  deleteClassroomResource,
  toggleResourceStatus,
} = require("../organisationController/classroom/classroomResources");








// --------------------------------------------------------------  Zoom




















// classroom resources apis
router.post("/create_classroom_resource", authenticateJWT, upload.single("file"), createClassroomResource);
router.post("/get_classroom_resources", authenticateJWT, getClassroomResources);
router.post("/update_classroom_resource", authenticateJWT, upload.single("file"), updateClassroomResource);
router.post("/delete_classroom_resource", authenticateJWT, deleteClassroomResource);
router.post("/toggle_resource_status", authenticateJWT, toggleResourceStatus);









//dashboard apis
router.get("/stats", authenticateJWT, getDashBoardStats),
  router.get("/userQueries", authenticateJWT, getUserQueries),
  router.get("/get_profile", authenticateJWT, getProfile);
router.put(
  "/edit_profile",
  authenticateJWT,
  upload.single("profile_pic"),
  updateUserProfile
);
router.get("/userLogs", authenticateJWT, getUserLogs);
router.get(
  "/org_performance_stats",
  authenticateJWT,
  getOrgPerformanceStatistics
);
router.get("/monthly_users", authenticateJWT, getUsersMonthlyWise);
router.get(
  "/monthly_created_course_and_classroom",
  authenticateJWT,
  getCreatedCourseAndClassroomMonthlyWise
);
router.get("/top_trainers", authenticateJWT, getTopTrainers);

//profile APIS
router.get("/profile", authenticateJWT, getOrgProfileDetails);
router.delete("/profile", authenticateJWT, deleteOrgProfileDetails);
router.post(
  "/user_bulk_upload",
  authenticateJWT,
  upload.single("file"),
  bulkUploadUsers
);
router.post("/trainee_analytics", authenticateJWT, TraineeAnalytics);
router.get("/sample_download", authenticateJWT, downloadSampleTemplate);

//course apis
router.get("/courses", authenticateJWT, getAllCourses);
router.get("/courses_api", authenticateJWT, getAllCoursesAPI);
router.get(
  "/get_video_details/:video_id/:module_id",
  authenticateJWT,
  getVideoDetailsById
);

router.get(
  "/performance/:course_id/:user_id/:page/:limit",
  authenticateJWT,
  getUserAllResults
);
router.get(
  "/assessment/:course_id/:user_id/:page/:limit",
  authenticateJWT,
  getUserAssessmentResults
);
router.get(
  "/assignment/:course_id/:user_id/:page/:limit",
  authenticateJWT,
  getAssignmentResults
);
router.get(
  "/survey/:course_id/:user_id/:page/:limit",
  authenticateJWT,
  getSurvyResults
);
router.get("/user_activity/:user_id", authenticateJWT, userActivity);
router.get("/course_list/:user_id", authenticateJWT, getUserCourseList);
router.get(
  "/course_performance/:course_id/:user_id",
  authenticateJWT,
  getPerformanceSummary
);

router.get("/single_course/:course_id", authenticateJWT, singleCourseDetails);
router.get("/get_course_categories", authenticateJWT, getCourseCategories);
router.get(
  "/course_approval_and_approved_and_rejected",
  authenticateJWT,
  getCourseApprovalANDApprovedANDRejected
);
router.post("/course/approve_course", authenticateJWT, courseApproving);
router.post("/course/reject_course", authenticateJWT, courseReject);
router.post(
  "/course_details_for_notification",
  authenticateJWT,
  getCourseDetailsByIdForNotification
);
router.post("/course_details_by_id", authenticateJWT, getCourseDetailsByID);
router.post("/check_course_purchase", authenticateJWT, checkCoursePurchase);
router.post(
  "/course/new",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  upload.single("banner_image"),
  createCourse
);
router.post('/get_assessment_analytics_using_gemini', authenticateJWT, getAssessmentAnalyticsUsingGemini);
router.put(
  "/course/update/:courseId",
  authenticateJWT,
  upload.single("banner_image"),
  updateCourse
);
router.delete("/course/:course_id", authenticateJWT, deleteCourse);
router.post("/course/send_approval", authenticateJWT, createCourseApproval);
router.put("/course/approve_course", authenticateJWT, approveCourseApproval);
router.get("/course_stats/:course_id", authenticateJWT, getCoursePassFailStats);
router.get(
  "/download_trainee_data/:course_id",
  authenticateJWT,
  downloadTraineeData
);
router.get(
  "/course_user_details/:course_id",
  authenticateJWT,
  getUserAssessmentDetails
);
router.get("/course_activity/:course_id", authenticateJWT, CourseUserActivity);
router.post("/course_surveys", authenticateJWT, getSurveysByCourseId);
router.post("/single_survey_details", authenticateJWT, getSingleSurveyDetails);
router.post("/course_assessments", authenticateJWT, getAssessmentsByCourseId);
router.get(
  "/assessment_details/:assessment_id",
  authenticateJWT,
  getAssessmentDetails
);
//module apis
router.get("/modules/:course_id", authenticateJWT, getModulesList);
router.get("/modules-content/:module_id", authenticateJWT, getModuleContent);
router.put(
  "/modules-content-active-deactive/:module_id/:content_id",
  authenticateJWT,
  ModuleContentStatus
);
router.post("/content-order-update", authenticateJWT, updateContentOrder);
router.post("/module/new", authenticateJWT, createCourseModule);
router.delete(
  "/course/:course_id/module/:module_id",
  authenticateJWT,
  deleteModule
);
router.put(
  "/course/:course_id/module/:module_id",
  authenticateJWT,
  moduleActiveDeactive
);

router.post(
  "/course/edit-document",
  authenticateJWT,
  upload.single("doc_url"),
  EditDocument
);
router.get("/get_document/:doc_id", authenticateJWT, getDocumentById);

router.put("/module/:module_id", authenticateJWT, editModule);

//assignment apis
router.get(
  "/assignment/module/:module_id",
  authenticateJWT,
  getAssignmentsByModule
);
router.post(
  "/assignment",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  upload.single("attachment_file"),
  createAssignment
);
router.put(
  "/assignment/:assignment_id",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  upload.single("attachment_file"),
  updateAssignment
);
router.put(
  "/assignment/status/:assignment_id",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  changeAssignmentStatus
);

router.post(
  "/document",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  upload.single("attachment_file"),
  createDocument
);

//assessment apis
router.get(
  "/assessment/module/:module_id",
  authenticateJWT,
  getAssessmentsByModule
);
router.post(
  "/assessment",
  authenticateJWT,
  //authorizePermissions("can_create_course"),
  createAssessment
);
router.delete("/assessment/:assessment_id", authenticateJWT, deleteAssessment);
router.put("/edit-assessment", authenticateJWT, updateAssessment);
router.get(
  "/questions/:module_id/assessment/:assessment_id",
  authenticateJWT,
  getQuestionsForAssessment
);
router.put(
  "/assessment/:assessment_id",
  authenticateJWT,
  changeAssessmentStatus
);

//question apis
router.get(
  "/course/get-questions/:module_id/:assessment_id",
  authenticateJWT,
  getQuestions
);
router.post(
  "/course/add-questions",
  authenticateJWT,
  upload.any(),
  addQuestion
);
router.delete(
  "/course/delete-question/:question_id",
  authenticateJWT,
  deleteQuestion
);

// Get videos for course/module for video selection in questions
router.get(
  "/course/videos/:course_id/:module_id?",
  authenticateJWT,
  getCourseVideos
);

router.post("/question/:question_id/option", authenticateJWT, addMCQOptions);
router.delete(
  "/question/:question_id/option",
  authenticateJWT,
  deleteMCQOption
);
router.post("/question/:question_id/answer", authenticateJWT, addCorrectAnswer);
router.put(
  "/course/edit-question/:question_id",
  authenticateJWT,
  upload.any(),
  editQuestion
);
router.put(
  "/course/edit-video",
  authenticateJWT,
  upload.single("upload_file"),
  editVideoContent
);

//video apis
// Multer error handler middleware
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    console.error('Multer error:', err);
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        success: false,
        data: {
          error_msg: 'File size too large. Maximum allowed size is 150MB.',
        },
      });
    }
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        data: {
          error_msg: 'Unexpected file field. Please check your file upload.',
        },
      });
    }
    return res.status(400).json({
      success: false,
      data: {
        error_msg: `File upload error: ${err.message}`,
      },
    });
  } else if (err) {
    console.error('File filter error:', err);
    return res.status(400).json({
      success: false,
      data: {
        error_msg: err.message || 'File upload failed',
      },
    });
  }
  next();
};

router.post("/video/upload", authenticateJWT, uploadNew, handleMulterError, addVideoAndDetails);

router.post(
  "/video/bulk_upload",
  authenticateJWT,
  upload.fields([{ name: "file" }]),
  bulkUploadVideos
);
router.delete(
  "/module-content/:content_id/:type",
  authenticateJWT,
  deleteModuleContent
);
router.get(
  "/course/:course_id/module/:module_id",
  authenticateJWT,
  getVideoDetails
);
router.put(
  "/video/:video_id",
  authenticateJWT,
  upload.single("video"),
  updateVideoDetails
);

//Classroom apis
/**
 * @swagger
 * /mainOrg/classroom/get_classes:
 *   post:
 *     summary: Get all classrooms
 *     description: Retrieves a paginated list of all classrooms with optional search functionality
 *     tags:
 *       - Classroom Management
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 default: 1
 *                 description: Page number for pagination
 *               limit:
 *                 type: integer
 *                 default: 10
 *                 description: Number of items per page
 *               search:
 *                 type: string
 *                 description: Search term to filter classrooms by name or code
 *     responses:
 *       200:
 *         description: List of classrooms retrieved successfully
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/get_classes",
  authenticateJWT,
  //authorizePermissions("can_view_classroom"),
  getAllClasses
);
router.get(
  "/classroom/students_approval",
  authenticateJWT,
  getAllStudentApproval
);
router.post(
  "/classroom/dashboard",
  authenticateJWT,
  //authorizePermissions("can_view_classroom"),
  classroomDashboard
);
router.post(
  "/classroom/visibility",
  authenticateJWT,
  //authorizePermissions("can_view_classroom"),
  changeTheVisibilityOfTabs
);

/**
 * @swagger
 * /mainOrg/classroom:
 *   post:
 *     summary: Create a new classroom
 *     description: Creates a new classroom with the provided details
 *     tags:
 *       - Classroom Management
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               cls_name:
 *                 type: string
 *                 description: Name of the classroom
 *                 example: "Advanced Python Programming"
 *               cls_desc:
 *                 type: string
 *                 description: Description of the classroom
 *                 example: "A comprehensive course on advanced Python programming concepts"
 *               collaboration_name:
 *                 type: string
 *                 description: Name for the collaboration group
 *                 example: "Python Experts Group"
 *               banner_img:
 *                 type: string
 *                 format: binary
 *                 description: Banner image for the classroom
 *             required:
 *               - cls_name
 *               - banner_img
 *     responses:
 *       200:
 *         description: Classroom created successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom",
  authenticateJWT,
  //authorizePermissions("can_create_classroom"),
  upload.single("banner_img"),
  createClassroom
);
/**
 * @swagger
 * /mainOrg/classroom:
 *   delete:
 *     summary: Delete a classroom
 *     description: Soft deletes a classroom by setting is_deleted to 1
 *     tags:
 *       - Classroom Management
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               classroom_id:
 *                 type: integer
 *                 description: ID of the classroom to delete
 *                 example: 1
 *             required:
 *               - classroom_id
 *     responses:
 *       201:
 *         description: Classroom deleted successfully
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
// Move trainee routes before the generic classroom/:classroom_id route to avoid conflicts
router.delete(
  "/classroom/trainee",
  authenticateJWT,
  removeTraineeFromClassroom
);

router.delete("/classroom/:classroom_id", authenticateJWT, deleteClassRoom);

router.get("/classroom/get_trainers", authenticateJWT, getAllTrainers);

router.get("/approve_deny_classroom", authenticateJWT, approveOrDenyClassroom);
/**
 * @swagger
 * /mainOrg/classroom:
 *   put:
 *     summary: Update classroom details
 *     description: Updates the details of an existing classroom
 *     tags:
 *       - Classroom Management
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom to update
 *                 example: 1
 *               user_id:
 *                 type: integer
 *                 description: ID of the user who created the classroom
 *                 example: 5
 *               cls_name:
 *                 type: string
 *                 description: Updated name of the classroom
 *                 example: "Advanced Python Programming - Updated"
 *               cls_desc:
 *                 type: string
 *                 description: Updated description of the classroom
 *                 example: "An updated comprehensive course on advanced Python programming concepts"
 *               collaboration_name:
 *                 type: string
 *                 description: Updated name for the collaboration group
 *                 example: "Python Experts Group - Updated"
 *               banner_img:
 *                 type: string
 *                 format: binary
 *                 description: Updated banner image for the classroom (optional)
 *             required:
 *               - class_id
 *               - user_id
 *               - cls_name
 *     responses:
 *       200:
 *         description: Classroom updated successfully
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.put(
  "/classroom",
  authenticateJWT,
  upload.single("banner_img"),
  updateClassroom
);

/**
 * @swagger
 * /mainOrg/classroom/status:
 *   put:
 *     summary: Activate or deactivate a classroom
 *     description: Updates the active status of a classroom
 *     tags:
 *       - Classroom Management
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom to update
 *                 example: 1
 *               status:
 *                 type: boolean
 *                 description: Status to set (true for activate, false for deactivate)
 *                 example: true
 *             required:
 *               - class_id
 *               - status
 *     responses:
 *       200:
 *         description: Classroom status updated successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.put("/classroom/status", authenticateJWT, activeDeactiveClassroom);

//live class apis
router.post("/classroom/get_live_class", authenticateJWT, getLiveClasses);
router.post("/classroom/live_class", authenticateJWT, createLiveClass);
router.post("/classroom/create_live_class", authenticateJWT, CreateZoomLive); //Delete zoom related apis here   DELETE
router.put(
  "/classroom/live_class/:live_class_id",
  authenticateJWT,
  updateLiveClass
);
router.delete(
  "/classroom/live_class/:live_class_id",
  authenticateJWT,
  deleteLiveClass
);
router.post(
  "/classroom/live_class/resend_mail",
  authenticateJWT,
  resendLiveClassMail
);
router.post(
  "/classroom/live_class/activate",
  authenticateJWT,
  activateLiveClass
);
router.post(
  "/classroom/live_class/deactivate",
  authenticateJWT,
  deactivateLiveClass
);

router.post(
  "/classroom/live_class/log_end_live_class",
  authenticateJWT,
  logEndLiveClass
);

// New Zoom Meeting SDK Live Class Routes
/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class:
 *   post:
 *     summary: Create a new live class with Zoom Meeting SDK
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               classroom_id:
 *                 type: integer
 *                 example: 1
 *               live_class_name:
 *                 type: string
 *                 example: "Introduction to React"
 *               live_class_description:
 *                 type: string
 *                 example: "Learn the basics of React framework"
 *               time_zone:
 *                 type: string
 *                 example: "Asia/Kolkata"
 *               class_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               start_time:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-01-15T10:00:00Z"
 *               duration:
 *                 type: string
 *                 example: "60 minutes"
 *             required:
 *               - classroom_id
 *               - live_class_name
 *               - class_date
 *               - start_time
 *               - duration
 *     responses:
 *       201:
 *         description: Live class created successfully
 *       400:
 *         description: Missing required fields
 *       500:
 *         description: Server error
 */
router.post("/classroom/zoom_live_class", authenticateJWT, createZoomLiveClass);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_classes:
 *   post:
 *     summary: Get all live classes for a classroom
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               classroom_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - classroom_id
 *     responses:
 *       200:
 *         description: Live classes retrieved successfully
 *       400:
 *         description: Missing classroom_id
 *       500:
 *         description: Server error
 */
router.post("/classroom/zoom_live_classes", authenticateJWT, getZoomLiveClasses);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class/{live_class_id}:
 *   put:
 *     summary: Update a live class
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: live_class_id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               live_class_name:
 *                 type: string
 *               live_class_description:
 *                 type: string
 *               time_zone:
 *                 type: string
 *               class_date:
 *                 type: string
 *                 format: date
 *               start_time:
 *                 type: string
 *                 format: date-time
 *               duration:
 *                 type: string
 *     responses:
 *       200:
 *         description: Live class updated successfully
 *       404:
 *         description: Live class not found
 *       500:
 *         description: Server error
 */
router.put("/classroom/zoom_live_class/:live_class_id", authenticateJWT, updateZoomLiveClass);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class/{live_class_id}:
 *   delete:
 *     summary: Delete a live class
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: live_class_id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Live class deleted successfully
 *       400:
 *         description: Cannot delete active meeting
 *       404:
 *         description: Live class not found
 *       500:
 *         description: Server error
 */
router.delete("/classroom/zoom_live_class/:live_class_id", authenticateJWT, deleteZoomLiveClass);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class/start:
 *   post:
 *     summary: Start a live class meeting
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               live_class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - live_class_id
 *     responses:
 *       200:
 *         description: Live class started successfully
 *       400:
 *         description: Meeting already started or missing live_class_id
 *       404:
 *         description: Live class not found
 *       500:
 *         description: Server error
 */
router.post("/classroom/zoom_live_class/start", authenticateJWT, startLiveClass);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class/end:
 *   post:
 *     summary: End a live class meeting
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               live_class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - live_class_id
 *     responses:
 *       200:
 *         description: Live class ended successfully
 *       400:
 *         description: Missing live_class_id
 *       404:
 *         description: Live class not found
 *       500:
 *         description: Server error
 */
router.post("/classroom/zoom_live_class/end", authenticateJWT, endLiveClass);

/**
 * @swagger
 * /mainOrg/classroom/zoom_live_class/join_token:
 *   post:
 *     summary: Generate join token for trainee
 *     tags:
 *       - Live Classes (Zoom SDK)
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               live_class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - live_class_id
 *     responses:
 *       200:
 *         description: Join token generated successfully
 *       400:
 *         description: Meeting not started or already ended
 *       404:
 *         description: Live class not found
 *       500:
 *         description: Server error
 */
router.post("/classroom/zoom_live_class/join_token", authenticateJWT, generateJoinToken);

// Debug endpoint for meeting verification
router.get("/classroom/zoom_live_class/debug/:meeting_id", authenticateJWT, debugMeeting);

//course approval apis
router.post("/approve_course/:course_id", authenticateJWT, approveCourse);
router.delete("/reject_course/:course_id", authenticateJWT, rejectCourse);
router.get("/pending_courses", authenticateJWT, getAllPendingCourses);
router.get("/approved_courses", authenticateJWT, getApprovedCourses);
router.post("/send_approval_course", authenticateJWT, sendApprovalcourse);
router.post(
  "/cancel_approval_course",
  authenticateJWT,
  cancelApprovalCourseRequest
);
router.get(
  "/user_details/trainer/:role_id",
  authenticateJWT,
  getParticularUserDetails
);

//user apis
router.get("/orgUser", authenticateJWT, getUserDetails);
router.post(
  "/orgUser",
  authenticateJWT,
  upload.single("profile_pic"),
  createOrganistionUser
);
router.put(
  "/editOrgUser/:user_id",
  authenticateJWT,
  upload.single("profile_pic"),
  editOrganisationUser
);
router.put("/deactivate_user/:user_id", authenticateJWT, activeDeactiveUser);
router.delete(
  "/delete_user/:user_id",
  authenticateJWT,
  deleteOrgProfileDetails
);

//roles and Permissions
router.post("/create_role", authenticateJWT, createRoleWithPermissions);
router.get("/get_permissions", authenticateJWT, getAllPermissions);
router.get("/roles", authenticateJWT, getRolesAndPermissions);
router.get("/permissions", authenticateJWT, getAllPermissions);
router.get(
  "/roles/:role_id/permissions",
  authenticateJWT,
  getRolePermissionsById
);

router.put("/roles/:role_id", authenticateJWT, updateRoleById);
router.post("/create_full_module_hierarchy", authenticateJWT, createGetFullModuleHierarchyWithActions);
router.get("/get_permissions_by_role_id", authenticateJWT, getPermissionsByRoleId);

// two new api 
router.get("/get_full_module_hierarchy", authenticateJWT, getFullModuleHierarchyWithActions);
router.post("/update_role_permission",authenticateJWT, updateRolePermission);
router.post("/insert_role_and_permissions", authenticateJWT, insertRoleAndPermissions);
router.post("/delete_role_and_permissions", authenticateJWT, deleteRoleAndPermissions);


router.put("/roles/:role_id", authenticateJWT, updateRoleById);
router.put("/roles/active/:role_id", authenticateJWT, activateRole);
router.put("/roles/deactive/:role_id", authenticateJWT, deactivateRole);
router.delete("/roles/:role_id", authenticateJWT, deleteRole);
router.post("/role", authenticateJWT, organizationRoles);
router.put("/role", authenticateJWT, editOrganizationRoles);



// create admin
router.post(
  "/create-admin-user",
  authenticateJWT,
  upload.fields([{ name: "profile_pic" }]),
  createAdminUser
);

//certificate template apis
router.post(
  "/certificate_template",
  authenticateJWT,
  upload.single("banner_img"),
  addCertificateTemplate
);
router.delete(
  "/certificate_template/:template_id",
  authenticateJWT,
  deleteCertificateTemplate
);
router.put(
  "/certificate_template/:template_id",
  authenticateJWT,
  updateCertificateTemplate
);
router.get("/certificate_template", authenticateJWT, getCertificateTemplates);
router.put("/certificate_template", authenticateJWT, changeCertificateStatus);

//trainee org
router.get(
  "/trainee_details",
  authenticateJWT,
  getTraineeDataWithCertificateCount
);
router.get(
  "/get_trainee_details/:trainee_id",
  authenticateJWT,
  getTraineeDetails
);
router.post(
  "/edit_trainee_details/:trainee_id",
  authenticateJWT,
  upload.single("profile_pic"),
  updateTraineeProfile
);

// Settings
router.put("/update_passwordAPI", authenticateJWT, updatePasswordAPI);
router.put(
  "/update_notification_settings_org",
  authenticateJWT,
  updateNotificationSettings
);
router.get(
  "/get_notification_settings_org",
  authenticateJWT,
  getNotificationSettings
);

//fetch trainer details
router.get("/trainer_details/:role_id", authenticateJWT, getTrainerDetails);

// create a Announcement routes for get, post, and update announcement
router.get("/announcement", authenticateJWT, getAnnouncements);
router.post("/announcement", authenticateJWT, createAnnouncement);
router.put("/announcements/:id", authenticateJWT, updateAnnouncement);
router.delete("/announcements/:id", authenticateJWT, deleteAnnouncement);
router.get("/get_announcment_role", authenticateJWT, getAnnouncementsRoles);

router.post(
  "/create_certificate",
  authenticateJWT,
  upload.single("upload_file"),
  createCertificate
);
router.put(
  "/edit_certificate",
  authenticateJWT,
  upload.single("upload_file"),
  editTemplates
);
router.delete("/delete_certificate/:id", authenticateJWT, deleteTemplate);
router.get("/get_certificate", authenticateJWT, getTemplates);
router.get("/get_course_certificate", authenticateJWT, getCertificates);
router.post(
  "/create_certificates_for_random_users",
  authenticateJWT,
  createCertificatesForRandomUsers
);

//Classroom trainee apis

/**
 * @swagger
 * /mainOrg/classroom/trainee:
 *   post:
 *     summary: Get all trainees for a specific classroom
 *     tags:
 *       - Classroom Trainee
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 example: 1
 *               page:
 *                 type: integer
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 default: 10
 *               search:
 *                 type: string
 *                 example: "john"
 *             required:
 *               - class_id
 *     responses:
 *       200:
 *         description: List of trainees with pagination
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     trainees:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           email:
 *                             type: string
 *                           mobile:
 *                             type: string
 *                           profile_pic_url:
 *                             type: string
 *                           joined_at:
 *                             type: string
 *                             format: date-time
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         totalRecords:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *       400:
 *         description: Missing or invalid input
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Internal server error
 */
router.post("/classroom/trainee", authenticateJWT, getAllTraineesForClassroom);

/**
 * @swagger
 * /mainOrg/classroom/trainee/add:
 *   post:
 *     summary: Add one or multiple trainees to a classroom
 *     tags:
 *       - Classroom Trainee
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [5, 6, 7]
 *                 description: Array of user IDs to add to the classroom (can also accept a single integer)
 *               class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - user_ids
 *               - class_id
 *     responses:
 *       201:
 *         description: Trainees added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: '3 trainee(s) added to classroom successfully'
 *                 addedCount:
 *                   type: integer
 *                   example: 3
 *                   description: Number of trainees successfully added
 *                 alreadyExistingCount:
 *                   type: integer
 *                   example: 1
 *                   description: Number of trainees that were already in the classroom
 *       400:
 *         description: Missing required fields or all trainees already added
 *       404:
 *         description: Classroom or one or more users not found
 *       500:
 *         description: Internal server error
 */
router.post("/classroom/trainee/add", authenticateJWT, addTraineeToClassroom);

/**
 * @swagger
 * /mainOrg/classroom/trainee/delete:
 *   post:
 *     summary: Remove one or multiple trainees from a classroom
 *     tags:
 *       - Classroom Trainee
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [5, 6, 7]
 *                 description: Array of user IDs to remove from the classroom (can also accept a single integer)
 *               class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - user_ids
 *               - class_id
 *     responses:
 *       200:
 *         description: Trainees removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Successfully removed 3 trainees from the classroom'
 *                 data:
 *                   type: object
 *                   properties:
 *                     removed_count:
 *                       type: integer
 *                       example: 3
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Internal server error
 */
router.post(
  "/classroom/trainee/delete",
  authenticateJWT,
  deleteTraineeFromClassroom
);

/**
 * @swagger
 * /mainOrg/classroom/get_all_trainees:
 *   post:
 *     summary: Get all trainees who are not in a specific classroom
 *     tags:
 *       - Classroom Trainee
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of records per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term to filter trainees by name or email
 *                 default: ''
 *     responses:
 *       200:
 *         description: List of trainees not in the classroom
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     trainees:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           first_name:
 *                             type: string
 *                           last_name:
 *                             type: string
 *                           email:
 *                             type: string
 *                           profile_image:
 *                             type: string
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         totalRecords:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         currentPage:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom not found or has been deleted
 *       500:
 *         description: Internal server error
 */

router.post(
  "/classroom/get_all_trainees",
  authenticateJWT,
  getAllUsersExceptClassroom
);

/**
 * @swagger
 * /mainOrg/classroom/trainee:
 *   delete:
 *     summary: Remove a trainee from a classroom
 *     tags:
 *       - Classroom Trainee
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: integer
 *                 example: 5
 *               class_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - user_id
 *               - class_id
 *     responses:
 *       200:
 *         description: Trainee removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Trainee removed from classroom successfully'
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Trainee not assigned to this classroom
 *       500:
 *         description: Internal server error
 */


//Classroom assessment copy to another classroom

router.post(
  "/classroom/assessment/copy",
  authenticateJWT,
  copyClassroomAssessment
);

//Classroom assessment apis
/**
 * @swagger
 * /mainOrg/classroom/assessments/all:
 *   post:
 *     summary: Get all assessments for a specific classroom
 *     description: Retrieves all assessments for a specific classroom with pagination, search, and date filtering capabilities
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom to fetch assessments for
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of records per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term to filter assessments by name
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Filter assessments created on or after this date (YYYY-MM-DD)
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: Filter assessments created on or before this date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of classroom assessments retrieved successfully
 *       400:
 *         description: Bad request - Missing class_id
 *       404:
 *         description: Classroom not found or has been deleted
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessments/all",
  authenticateJWT,
  getTraineeClassroomAssessments
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/add:
 *   post:
 *     summary: Add a new assessment to a classroom
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assessment_name
 *               - duration
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_name:
 *                 type: string
 *                 description: Name of the assessment
 *               activate_time:
 *                 type: string
 *                 format: date-time
 *                 description: Date and time when the assessment becomes available
 *               deactivate_time:
 *                 type: string
 *                 format: date-time
 *                 description: Date and time when the assessment becomes unavailable
 *               duration:
 *                 type: integer
 *                 description: Duration of the assessment in minutes
 *               reward_points:
 *                 type: integer
 *                 description: Optional reward points for completing the assessment
 *               is_time_based:
 *                 type: integer
 *                 enum: [0, 1]
 *                 default: 0
 *                 description: Whether the assessment's active status should be automatically managed based on time (0=manual, 1=automatic)
 *               is_active:
 *                 type: integer
 *                 enum: [0, 1]
 *                 default: 0
 *                 description: Whether the assessment is active (0=inactive, 1=active)
 *               pass_percentage:
 *                 type: integer
 *                 description: Percentage required to pass the assessment (0-100)
 *                 default: 100
 *     responses:
 *       200:
 *         description: Assessment added successfully
 *       400:
 *         description: Bad request - Missing required fields
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/add",
  authenticateJWT,
  addClassroomAssessment
);

/**
 * @swagger
 * /mainOrg/classroom/assessment:
 *   put:
 *     summary: Update an existing classroom assessment
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 example: 1
 *               assessment_id:
 *                 type: integer
 *                 example: 5
 *               assessment_name:
 *                 type: string
 *                 example: "Updated Final Exam"
 *               activate_time:
 *                 type: string
 *                 format: date-time
 *                 example: "2025-04-15T10:00:00Z"
 *               deactivate_time:
 *                 type: string
 *                 format: date-time
 *                 example: "2025-04-15T12:00:00Z"
 *               duration:
 *                 type: integer
 *                 description: Duration in minutes
 *                 example: 120
 *               reward_points:
 *                 type: integer
 *                 example: 150
 *               is_active:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Whether the assessment is active (0=inactive, 1=active)
 *                 example: 1
 *               is_time_based:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Whether the assessment's active status should be automatically managed based on time (0=manual, 1=automatic)
 *                 example: 0
 *               pass_percentage:
 *                 type: integer
 *                 description: Percentage required to pass the assessment (0-100)
 *                 default: 100
 *             required:
 *               - class_id
 *               - assessment_id
 *               - assessment_name
 *               - activate_time
 *               - deactivate_time
 *               - duration
 *     responses:
 *       200:
 *         description: Assessment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Classroom assessment updated successfully'
 *                 assessment:
 *                   type: object
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom or assessment not found
 *       500:
 *         description: Internal server error
 */
router.put("/classroom/assessment", authenticateJWT, updateClassroomAssessment);

/**
 * @swagger
 * /mainOrg/classroom/assessment:
 *   delete:
 *     summary: Delete a classroom assessment
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 example: 1
 *               assessment_id:
 *                 type: integer
 *                 example: 5
 *             required:
 *               - class_id
 *               - assessment_id
 *     responses:
 *       200:
 *         description: Assessment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Classroom assessment deleted successfully'
 *                 assessment:
 *                   type: object
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Assessment not found
 *       500:
 *         description: Internal server error
 */
router.post(
  "/classroom/assessment",
  authenticateJWT,
  deleteClassroomAssessment
);

//Question bank apis

// Get question_tags
router.get("/question_tags", authenticateJWT, getQuestionTags);

/**
 * @swagger
 * /mainOrg/question_bank/questions:
 *   post:
 *     summary: Get all questions with pagination and search
 *     tags:
 *       - Question Bank
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: integer
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 default: 10
 *               search:
 *                 type: string
 *                 example: ""
 *     responses:
 *       200:
 *         description: List of questions with pagination
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Questions retrieved successfully'
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     questions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           question_name:
 *                             type: string
 *                           question_type:
 *                             type: string
 *                           is_active:
 *                             type: boolean
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                           options:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                 option_text:
 *                                   type: string
 *                                 option_value:
 *                                   type: string
 *                                 is_correct:
 *                                   type: boolean
 *                                 is_active:
 *                                   type: boolean
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         totalRecords:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *       500:
 *         description: Internal server error
 */
router.post("/question_bank/questions", authenticateJWT, qbGetQuestions);

router.post("/delete_question_tag", authenticateJWT, deleteQuestionTag);

/**
 * @swagger
 * /mainOrg/question_bank/add_question:
 *   post:
 *     summary: Create a new question with options
 *     tags:
 *       - Question Bank
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question_name:
 *                 type: string
 *                 example: "What is the capital of France?"
 *               question_type:
 *                 type: string
 *                 example: "multiple_choice"
 *               options:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     option_text:
 *                       type: string
 *                       example: "Paris"
 *                     option_value:
 *                       type: string
 *                       example: "paris"
 *                     is_correct:
 *                       type: boolean
 *                       example: true
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *             required:
 *               - question_name
 *               - question_type
 *               - options
 *     responses:
 *       201:
 *         description: Question created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 status:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 'Question created successfully'
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     question_name:
 *                       type: string
 *                     question_type:
 *                       type: string
 *                     is_active:
 *                       type: boolean
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                     options:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           option_text:
 *                             type: string
 *                           option_value:
 *                             type: string
 *                           is_correct:
 *                             type: boolean
 *                           is_active:
 *                             type: boolean
 *       400:
 *         description: Missing required fields or no correct option
 *       500:
 *         description: Internal server error
 */
router.post("/question_bank/add_question", authenticateJWT, qbCreateQuestion);

/**
 * @swagger
 * /mainOrg/question_bank/update_question:
 *   put:
 *     summary: Update an existing question and its options
 *     tags:
 *       - Question Bank
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question_id:
 *                 type: integer
 *                 example: 1
 *               question_name:
 *                 type: string
 *                 example: "What is the capital of France? (Updated)"
 *               question_type:
 *                 type: string
 *                 example: "multiple_choice"
 *               is_active:
 *                 type: boolean
 *                 example: true
 *               options:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     option_text:
 *                       type: string
 *                       example: "Paris"
 *                     option_value:
 *                       type: string
 *                       example: "paris"
 *                     is_correct:
 *                       type: boolean
 *                       example: true
 *                     is_active:
 *                       type: boolean
 *                       example: true
 *             required:
 *               - question_id
 *               - question_name
 *               - question_type
 *               - options
 *     responses:
 *       200:
 *         description: Question updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 'Question updated successfully'
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     question_name:
 *                       type: string
 *                     question_type:
 *                       type: string
 *                     is_active:
 *                       type: boolean
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                     options:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           option_text:
 *                             type: string
 *                           option_value:
 *                             type: string
 *                           is_correct:
 *                             type: boolean
 *                           is_active:
 *                             type: boolean
 *       400:
 *         description: Missing required fields or no correct option
 *       404:
 *         description: Question not found
 *       500:
 *         description: Internal server error
 */
router.put("/question_bank/update_question", authenticateJWT, qbUpdateQuestion);

/**
 * @swagger
 * /mainOrg/question_bank/delete_question:
 *   delete:
 *     summary: Soft delete a question and its options
 *     tags:
 *       - Question Bank
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question_id:
 *                 type: integer
 *                 example: 1
 *             required:
 *               - question_id
 *     responses:
 *       200:
 *         description: Question deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Question deleted successfully'
 *       400:
 *         description: Missing question ID
 *       404:
 *         description: Question not found
 *       500:
 *         description: Internal server error
 */
router.post(
  "/question_bank/delete_question",
  authenticateJWT,
  qbDeleteQuestion
);

//Assessment Question APIS

/**
 * @swagger
 * /mainOrg/classroom/assessment/add_questions:
 *   post:
 *     summary: Add questions to an assessment
 *     tags: [Classroom Assessments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assessment_id
 *               - questionIds
 *               - class_id
 *             properties:
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               questionIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of question IDs to add to the assessment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *     responses:
 *       200:
 *         description: Questions added successfully
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom or assessment not found
 *       500:
 *         description: Server error
 */

router.post(
  "/classroom/assessment/add_questions",
  authenticateJWT,
  addQuestionsToAssessment
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/remove_question:
 *   post:
 *     summary: Remove a question from an assessment
 *     tags: [Classroom Assessments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - question_id
 *               - assessment_id
 *               - class_id
 *             properties:
 *               question_id:
 *                 type: integer
 *                 description: ID of the question to remove
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *     responses:
 *       200:
 *         description: Question removed successfully
 *       400:
 *         description: Missing required fields
 *       500:
 *         description: Server error
 */

router.post(
  "/classroom/assessment/remove_question",
  authenticateJWT,
  removeQuestionFromAssessment
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/trainee-results:
 *   post:
 *     summary: Get assessment results for trainees in a classroom
 *     tags: [Classroom Assessments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assessment_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               page:
 *                 type: integer
 *                 default: 1
 *                 description: Page number for pagination
 *               limit:
 *                 type: integer
 *                 default: 10
 *                 description: Number of results per page
 *               search:
 *                 type: string
 *                 description: Search term to filter results by username
 *     responses:
 *       200:
 *         description: Assessment results retrieved successfully
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom or assessment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/trainee-results",
  authenticateJWT,
  getTraineeAssessmentResults
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/trainee-result:
 *   post:
 *     summary: Get detailed assessment results for a specific trainee
 *     tags: [Classroom Assessments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - class_id
 *               - assessment_id
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: ID of the trainee
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *     responses:
 *       200:
 *         description: Detailed assessment results retrieved successfully
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: Classroom, assessment, or user not found
 *       500:
 *         description: Server error
 */

router.post(
  "/classroom/assessment/trainee-result",
  authenticateJWT,
  getAssessmentResultsforTrainee
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/questions/get:
 *   post:
 *     summary: Get all questions for a specific assessment for admin
 *     description: Retrieves all questions for a specific assessment with pagination and search capabilities
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assessment_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of records per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term to filter questions by content
 *     responses:
 *       200:
 *         description: Assessment questions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 101
 *                       question_text:
 *                         type: string
 *                         example: What is the capital of France?
 *                       options:
 *                         type: array
 *                         description: Question options (for MCQs)
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                             value:
 *                               type: string
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalRecords:
 *                       type: integer
 *                       example: 50
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *       400:
 *         description: Bad request - Missing required parameters
 *       404:
 *         description: Classroom or assessment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/questions/get",
  authenticateJWT,
  getAssessmentQuestions
);

/**
 * @swagger
 * /mainOrg/classroom/assessment/available-questions:
 *   post:
 *     summary: Get available questions for an assessment from Question Bank
 *     description: Retrieves all question bank questions that are not yet added to a specific assessment
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assessment_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assessment_id:
 *                 type: integer
 *                 description: ID of the assessment
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of records per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term to filter questions by name or type
 *     responses:
 *       200:
 *         description: Available questions retrieved successfully
 *       400:
 *         description: Bad request - Missing required parameters
 *       404:
 *         description: Classroom or assessment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/assessment/available-questions",
  authenticateJWT,
  getAvailableQuestionsForAssessment
);

/**
 * @swagger
 * /mainOrg/classroom/{class_id}/assessment/{assessment_id}/analytics:
 *   get:
 *     summary: Get assessment analytics for a specific classroom assessment
 *     description: Retrieves analytics data showing passed/failed/total trainees for a classroom assessment
 *     tags:
 *       - Classroom Assessments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: class_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the classroom
 *       - in: path
 *         name: assessment_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the assessment
 *     responses:
 *       200:
 *         description: Assessment analytics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Assessment analytics retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     total_trainees:
 *                       type: integer
 *                       example: 145
 *                     passed:
 *                       type: integer
 *                       example: 10
 *                     failed:
 *                       type: integer
 *                       example: 20
 *                     not_attempted:
 *                       type: integer
 *                       example: 115
 *                     passing_percentage:
 *                       type: integer
 *                       example: 70
 *       400:
 *         description: Bad request - Missing required parameters
 *       404:
 *         description: Assessment not found
 *       500:
 *         description: Server error
 */
router.get(
  "/classroom/:class_id/assessment/:assessment_id/analytics",
  authenticateJWT,
  getAssessmentAnalytics
);

/**
 * @swagger
 * /mainOrg/classroom/create_assignment:
 *   post:
 *     summary: Create a new classroom assignment
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *                 example: 1
 *               title:
 *                 type: string
 *                 description: Title of the assignment
 *                 example: "Final Project Submission"
 *               description:
 *                 type: string
 *                 description: Detailed description of the assignment
 *                 example: "Submit your final project with all required documentation"
 *               due_date:
 *                 type: string
 *                 format: date-time
 *                 description: Deadline for the assignment
 *                 example: "2025-06-15T23:59:59"
 *               attachment_file:
 *                 type: string
 *                 format: binary
 *                 description: Assignment document or reference material
 *             required:
 *               - class_id
 *               - title
 *     responses:
 *       201:
 *         description: Assignment created successfully
 *       400:
 *         description: Bad request - Missing required fields
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/create_assignment",
  authenticateJWT,
  upload.single("attachment_file"),
  createClassroomAssignment
);

/**
 * @swagger
 * /mainOrg/classroom/update_assignment:
 *   put:
 *     summary: Update an existing classroom assignment
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assignment_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the assignment to update
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *                 example: 1
 *               title:
 *                 type: string
 *                 description: Title of the assignment
 *                 example: "Updated Project Submission"
 *               description:
 *                 type: string
 *                 description: Detailed description of the assignment
 *                 example: "Updated description for the final project"
 *               due_date:
 *                 type: string
 *                 format: date-time
 *                 description: Deadline for the assignment
 *                 example: "2025-06-20T23:59:59"
 *               attachment_file:
 *                 type: string
 *                 format: binary
 *                 description: Assignment document or reference material
 *     responses:
 *       200:
 *         description: Assignment updated successfully
 *       400:
 *         description: Bad request - Missing assignment ID
 *       404:
 *         description: Assignment or classroom not found
 *       500:
 *         description: Server error
 */
router.put(
  "/classroom/update_assignment",
  authenticateJWT,
  upload.single("attachment_file"),
  updateClassroomAssignment
);

/**
 * @swagger
 * /mainOrg/classroom/delete_assignment:
 *   delete:
 *     summary: Delete (soft delete) a classroom assignment
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assignment_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the assignment to delete
 *     responses:
 *       200:
 *         description: Assignment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Assignment deleted successfully"
 *       400:
 *         description: Bad request - Missing assignment ID
 *       404:
 *         description: Assignment not found or already deleted
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/delete_assignment",
  authenticateJWT,
  deleteClassroomAssignment
);

/**
 * @swagger
 * /mainOrg/classroom/get_assignments:
 *   post:
 *     summary: Get classroom assignments with filtering and pagination
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *                 example: 1
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 example: 1
 *               limit:
 *                 type: integer
 *                 description: Number of items per page
 *                 example: 10
 *               search:
 *                 type: string
 *                 description: Search term for filtering assignments by title or description
 *                 example: "project"
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Filter assignments assigned after this date
 *                 example: "2025-05-01"
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: Filter assignments assigned before this date
 *                 example: "2025-06-30"
 *             required:
 *               - class_id
 *     responses:
 *       200:
 *         description: Assignments retrieved successfully
 *       400:
 *         description: Bad request - Missing class_id
 *       404:
 *         description: Classroom not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/get_assignments",
  authenticateJWT,
  getClassroomAssignments
);

/**
 * @swagger
 * /mainOrg/classroom/get_class_assignment:
 *   post:
 *     summary: Get a single classroom assignment by ID
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_id
 *               - class_id
 *             properties:
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment to retrieve
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom to retrieve
 *     responses:
 *       200:
 *         description: Assignment retrieved successfully
 *       400:
 *         description: Bad request - Missing assignment ID or class ID
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/get_class_assignment",
  authenticateJWT,
  getClassroomAssignmentById
);

/**
 * @swagger
 * /mainOrg/classroom/grade_assignment:
 *   post:
 *     summary: Grade a classroom assignment submission
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - class_id
 *               - assignment_id
 *               - marks_obtained
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: ID of the student
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               marks_obtained:
 *                 type: number
 *                 description: Marks given to the submission
 *               feedback:
 *                 type: string
 *                 description: Optional feedback for the student
 *     responses:
 *       200:
 *         description: Assignment graded successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Assignment or submission not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/grade_assignment",
  authenticateJWT,
  gradeClassroomAssignment
);

/**
 * @swagger
 * /mainOrg/classroom/get_assignment_submissions:
 *   post:
 *     summary: Get all submissions for a specific assignment
 *     tags: [Classroom Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - class_id
 *               - assignment_id
 *             properties:
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of items per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term for filtering submissions by student name or email
 *     responses:
 *       200:
 *         description: Assignment submissions retrieved successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/get_assignment_submissions",
  authenticateJWT,
  getClassroomAssignmentSubmissions
);

router.post(
  "/classroom/user_assignment_status",
  authenticateJWT,
  userAssignmentStatus
);

/**
 * @swagger
 * /mainOrg/classroom/add_assignment_question:
 *   post:
 *     summary: Add a question to an assignment
 *     tags: [Classroom Assignment Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_id
 *               - class_id
 *               - question_text
 *               - response_type
 *             properties:
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               question_text:
 *                 type: string
 *                 description: Text of the question
 *               response_type:
 *                 type: string
 *                 description: Type of response (e.g., text, multiple_choice)
 *               marks:
 *                 type: integer
 *                 description: Marks for the question
 *               question_order:
 *                 type: integer
 *                 description: Order of the question
 *     responses:
 *       201:
 *         description: Question added successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/add_assignment_question",
  authenticateJWT,
  upload.single("media"),
  addClassroomAssignmentQuestion
);

/**
 * @swagger
 * /mainOrg/classroom/update_assignment_question:
 *   put:
 *     summary: Update an assignment question
 *     tags: [Classroom Assignment Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - question_id
 *               - assignment_id
 *               - class_id
 *             properties:
 *               question_id:
 *                 type: integer
 *                 description: ID of the question to update
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               question_text:
 *                 type: string
 *                 description: Text of the question
 *               response_type:
 *                 type: string
 *                 description: Type of response
 *               marks:
 *                 type: integer
 *                 description: Marks for the question
 *               question_order:
 *                 type: integer
 *                 description: Order of the question
 *     responses:
 *       200:
 *         description: Question updated successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Question not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/update_assignment_question",
  authenticateJWT,
  upload.single("media"),
  updateClassroomAssignmentQuestion
);

router.post(
  "/classroom/get_user_classroom_assignments_questions",
  authenticateJWT,
  getUserClassroomAssignmentsQuestions
);

router.post(
  "/classroom/submit_user_classroom_assignment_response",
  authenticateJWT,
  upload.single("media"),
  submitUserClassroomAssignmentResponse
);

/**
 * @swagger
 * /mainOrg/classroom/delete_assignment_question:
 *   post:
 *     summary: Delete an assignment question
 *     tags: [Classroom Assignment Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - question_id
 *               - assignment_id
 *               - class_id
 *             properties:
 *               question_id:
 *                 type: integer
 *                 description: ID of the question to delete
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *     responses:
 *       200:
 *         description: Question deleted successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Question not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/delete_assignment_question",
  authenticateJWT,
  deleteClassroomAssignmentQuestion
);

/**
 * @swagger
 * /mainOrg/classroom/get_assignment_questions:
 *   post:
 *     summary: Get questions for a specific assignment
 *     tags: [Classroom Assignment Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_id
 *               - class_id
 *             properties:
 *               assignment_id:
 *                 type: integer
 *                 description: ID of the assignment
 *               class_id:
 *                 type: integer
 *                 description: ID of the classroom
 *               page:
 *                 type: integer
 *                 description: Page number for pagination
 *                 default: 1
 *               limit:
 *                 type: integer
 *                 description: Number of items per page
 *                 default: 10
 *               search:
 *                 type: string
 *                 description: Search term for filtering questions
 *     responses:
 *       200:
 *         description: Questions retrieved successfully
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.post(
  "/classroom/get_assignment_questions",
  authenticateJWT,
  getClassroomAssignmentQuestions
);

// About Organization Routes
router.post("/about-organization", authenticateJWT, getAboutOrganization);
router.post("/about-organization/add", authenticateJWT, addAboutOrganization);
router.put("/about-organization/:id", authenticateJWT, editAboutOrganization);
router.put("/about-organization/status/:id", authenticateJWT, statusAboutOrganization);
router.delete("/about-organization/:id", authenticateJWT, deleteAboutOrganization);

// FAQ Routes
router.post("/faqs", authenticateJWT, getFAQs);
router.post("/faqs/add", authenticateJWT, addFAQ);
router.put("/faqs/:id", authenticateJWT, updateFAQ);
router.put("/faqs/status/:id", authenticateJWT, statusFAQ);
router.delete("/faqs/:id", authenticateJWT, deleteFAQ);

// Payment Routes
router.post("/payments", authenticateJWT, getPayments);
router.get("/payments/:id", authenticateJWT, getPaymentById);

// Ticket Routes
router.post("/tickets", authenticateJWT, getTickets);
router.put("/tickets/:id/response", authenticateJWT, sendTicketResponse);

// Dashboard Routes
router.get("/get_dashboard_data", authenticateJWT, getDashboardData);


module.exports = router;
