{"ast": null, "code": "import { DELETE, GET, POST, PUT, UPLOAD_FILE, UPLOAD_FILE_WITH_METHOD } from \"./apiController\";\nconst endpoint = \"/mainOrg\";\n\n// --------------------------------- Dashboard\n\n// /get_dashboard_data\nexport const getDashboardData = () => {\n  return GET(endpoint + `/get_dashboard_data`);\n};\n\n// /trainee_analytics\nexport const getTraineeAnalytics = data => {\n  return POST(endpoint + `/trainee_analytics`, data);\n};\n\n// --------------------------------- Prifle\n\n// ---------------------------------- Courses\n\n// get all courses\nexport const courses_api = () => {\n  return GET(endpoint + `/courses_api`);\n};\n\n// ---------------------------------- Course Analytics\n\n// get course performance stats (enrollments, completed, in progress, etc.)\nexport const coursePerformance = courseId => {\n  return GET(endpoint + `/course_stats/${courseId}`);\n};\n\n// get course performance details with pagination and filters\nexport const coursePerformanceDetails = data => {\n  return GET(endpoint + `/course_user_details/${data.course_id}?search=${data.search}&page=${data.page}&limit=${data.limit}&status=${data.status}`);\n};\n\n// get course surveys\nexport const getCourseSurveys = data => {\n  return POST(endpoint + `/course_surveys`, data);\n};\n\n// get course assessments\nexport const getCourseAssessments = data => {\n  return POST(endpoint + `/course_assessments`, data);\n};\n\n// get assessment details\nexport const getAssessmentDetails = assessmentId => {\n  return GET(endpoint + `/assessment_details/${assessmentId}`);\n};\n\n//get_assessment_analytics_using_gemini\nexport const getAssessmentAnalyticsUsingGemini = data => {\n  return POST(\"/get_assessment_analytics_using_gemini\", data);\n};\n\n// get single survey details\nexport const singleSurveyDetails = data => {\n  return POST(endpoint + `/single_survey_details`, data);\n};\n\n// get course user activity\nexport const courseUserActivity = courseId => {\n  return GET(endpoint + `/course_activity/${courseId}`);\n};\n\n// delete course\nexport const courseDelete = data => {\n  return DELETE(endpoint + `/course/${data.course_id}`);\n};\n\n// send approval course\nexport const sendApprovalCourse = data => {\n  return POST(endpoint + `/send_approval_course`, data);\n};\n\n// cancel approval course\nexport const cancelApprovalCourse = data => {\n  return POST(endpoint + `/cancel_approval_course`, data);\n};\n\n// get course module\nexport const getCourseModule = data => {\n  return GET(endpoint + `/modules/${data.id}?search=${data.search || \"\"}`);\n};\n\n// create module\nexport const createModule = data => {\n  return POST(endpoint + `/module/new`, data);\n};\n\n// delete module\nexport const deleteModule = async data => {\n  return DELETE(endpoint + `/course/${data.course_id}/module/${data.module_id}`);\n};\n\n// update module\nexport const updateModule = (moduleId, data) => {\n  return PUT(endpoint + `/module/${moduleId}`, data);\n};\n\n// update module status\nexport const moduleStatus = data => {\n  return PUT(endpoint + `/course/${data.course_id}/module/${data.module_id}`, {\n    status: data.status\n  });\n};\n\n// get module content\nexport const getCourseModuleContent = data => {\n  return GET(endpoint + `/modules-content/${data.module_id}`);\n};\n\n// create assessment\nexport const createAssessmentAPI = data => {\n  return POST(endpoint + `/assessment`, data); // Calls the backend API to create an assessment\n};\nexport const createDocument = data => {\n  return UPLOAD_FILE(endpoint + `/document`, data);\n};\nexport const createVideoAPI = data => {\n  return UPLOAD_FILE(endpoint + `/video/upload`, data);\n};\nexport const deleteContentById = async data => {\n  return DELETE(endpoint + `/module-content/${data.content_id}/${data.type}`);\n};\nexport const changeContentstatus = (params, data) => {\n  return PUT(endpoint + `/modules-content-active-deactive/${params.module_id}/${params.content_id}`, data);\n};\n\n// update content order\nexport const updateContentOrder = data => {\n  return POST(endpoint + `/content-order-update`, data);\n};\nexport const getVideoDetailsById = data => {\n  return GET(endpoint + `/get_video_details/${data.video_id}/${data.module_id}`);\n};\nexport const getCourseReviews = (course_id, video_id) => {\n  const queryParams = video_id ? `course_id=${course_id}&video_id=${video_id}` : `course_id=${course_id}`;\n  return GET(`/org/get_course_reviews?${queryParams}`);\n};\nexport const EditVideoContent = data => {\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/edit-video`, data, \"PUT\");\n};\n_c = EditVideoContent;\nexport const editDocument = data => {\n  return UPLOAD_FILE(endpoint + `/course/edit-document`, data); // Use POST method as defined in backend route\n};\n\n//   get_document/:doc_i\nexport const getDocumentById = data => {\n  return GET(endpoint + `/get_document/${data.doc_id}`);\n};\n\n// get module questions\nexport const getModuleQuestions = data => {\n  return GET(endpoint + `/course/get-questions/${data.module_id}/${data.assessment_id}`);\n};\nexport const getCourseVideos = data => {\n  const moduleParam = data.module_id ? `/${data.module_id}` : \"\";\n  return GET(endpoint + `/course/videos/${data.course_id}${moduleParam}`);\n};\nexport const addQuestion = async data => {\n  try {\n    // If data is FormData, use it directly; otherwise use POST\n    let response;\n    if (data instanceof FormData) {\n      console.log(\"Using UploadFormData for form data\");\n      response = await UPLOAD_FILE(endpoint + `/course/add-questions`, data);\n      console.log(\"Response from UploadFormData:\", response);\n    } else {\n      console.log(\"Using POST for JSON data\");\n      response = await POST(endpoint + `/course/add-questions`, data);\n      console.log(\"Response from POST:\", response);\n    }\n\n    // If response is undefined, create a default error response\n    if (!response) {\n      console.warn(\"Response from server is undefined, creating default response\");\n      // Check if the form data was sent successfully despite no response\n      return {\n        success: true,\n        message: \"Question may have been added, but no response was received\"\n      };\n    }\n\n    // If we have a response with success property, it's already formatted correctly\n    if (response.success !== undefined) {\n      console.log(\"Response already has success property, returning directly:\", response);\n      return response;\n    }\n\n    // If we get here, we have a response but it's not in the expected format\n    console.log(\"Formatting response to expected format:\", response);\n    return {\n      success: true,\n      message: \"Question added successfully\",\n      data: response\n    };\n  } catch (error) {\n    console.error(\"Error in addQuestion:\", error);\n    // Return an error object rather than throwing\n    return {\n      success: false,\n      error: error.toString()\n    };\n  }\n};\nexport const deleteQuestion = data => {\n  return DELETE(endpoint + `/course/delete-question/${data.question_id}`);\n};\nexport const editQuestion = data => {\n  // If data is FormData, use it directly with PUT method; otherwise use PUT\n  if (data instanceof FormData) {\n    const questionId = data.get(\"id\");\n    return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/edit-question/${questionId}`, data, \"PUT\");\n  } else {\n    const questionId = data.id;\n    return PUT(endpoint + `/course/edit-question/${questionId}`, data);\n  }\n};\nexport const editAssessment = data => {\n  return PUT(endpoint + `/edit-assessment`, data);\n};\n\n// create course\nexport const getCourseCertificate = (search = \"\") => {\n  return GET(endpoint + `/get_course_certificate${search ? `?search=${search}` : \"\"}`);\n};\nexport const createNewCourse = data => {\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/new`, data);\n};\n\n//   /course_details_by_id uign payload\nexport const getCourseDetailsById = data => {\n  return POST(endpoint + `/course_details_by_id`, data);\n};\nexport const updateCourseDetails = (courseId, data) => {\n  return PUT(endpoint + `/course/update/${courseId}`, data);\n};\nexport const getCourseList = data => {\n  return GET(endpoint + `/courses?page=${data.page}&limit=${data.limit}&search=${data.search || \"\"}&status=${data.status || \"all\"}`);\n};\n\n// ---------------------------------------------------- course ApprovalRequest\n\n// /get_course_categories\nexport const getCourseApprovalANDApprovedANDRejected = () => {\n  return GET(endpoint + `/course_approval_and_approved_and_rejected`);\n};\nexport const approveCourse = data => {\n  return POST(endpoint + `/approve_course/${data.id}`);\n};\nexport const RejectApprovalCourse = data => {\n  return POST(endpoint + `/course/reject_course`, {\n    course_id: data.id,\n    rejected_note: data.reason\n  });\n};\n\n//   ------------------------------------------------------------ classroom\n_c2 = RejectApprovalCourse;\nexport const classroomDashboard = data => {\n  return POST(endpoint + `/classroom/dashboard`, data);\n};\n\n// /classroom/visibility\nexport const classroomVisibility = data => {\n  return POST(endpoint + `/classroom/visibility`, data);\n};\n\n// --Get classroom data api\nexport const getClassroomDetails = data => {\n  return POST(endpoint + `/classroom/get_classes`, data);\n};\nexport const getAllTrainers = data => {\n  return GET(endpoint + `/classroom/get_trainers`);\n};\nexport const createClassroom = data => {\n  return UPLOAD_FILE(endpoint + `/classroom`, data);\n};\n\n// --Update classroom data api\nexport const updateClassroomDetails = data => {\n  return PUT(endpoint + `/classroom`, data);\n};\n\n// -- Update classroom status api\nexport const updateClassroomStatus = data => {\n  return PUT(endpoint + `/classroom/status`, data);\n};\n\n// -- Delete classroom api\nexport const deleteClassroom = data => {\n  return DELETE(endpoint + `/classroom/${data.classroom_id}`);\n};\n\n// -----------------------------------------------  classroom Trainees\n\n//-- get trainees details api\nexport const getTrainees = data => {\n  return POST(endpoint + `/classroom/trainee`, data);\n};\nexport const removeTraineeFromClassroom = ({\n  user_id,\n  class_id\n}) => {\n  return DELETE(endpoint + `/classroom/trainee?user_id=${user_id}&class_id=${class_id}`);\n};\n\n// -- get all user from organization\nexport const getAllUsersExceptClassroom = data => {\n  return POST(endpoint + `/classroom/get_all_trainees`, data);\n};\n\n//--- Add multiple trainees to classroom\nexport const addTraineesToClassroom = data => {\n  return POST(endpoint + `/classroom/trainee/add`, data);\n};\n\n//--- Copy assesss to one class to another\nexport const deleteMultipleTrainees = data => {\n  return POST(endpoint + `/classroom/trainee/delete`, data);\n};\n\n//   -------------------------------------------- classroom assessments\n\nexport const getAssignmentsForClassroom = data => {\n  return POST(endpoint + \"/classroom/get_assignments\", data);\n};\nexport const addClassroomAssignment = data => {\n  // If data is FormData (contains file), use UploadFormData, otherwise use regular POST\n  if (data instanceof FormData) {\n    return UPLOAD_FILE(endpoint + `/classroom/create_assignment`, data);\n  } else {\n    return POST(endpoint + `/classroom/create_assignment`, data);\n  }\n};\nexport const deleteClassroomAssignment = data => {\n  return POST(endpoint + `/classroom/delete_assignment`, data);\n};\nexport const updateClassroomAssignment = data => {\n  // If data is FormData (contains file), use UploadFormData with PUT, otherwise use regular PUT\n  if (data instanceof FormData) {\n    return UPLOAD_FILE(endpoint + `/classroom/update_assignment`, data, \"PUT\");\n  } else {\n    return PUT(endpoint + `/classroom/update_assignment`, data);\n  }\n};\n\n// ------------------------------------------------- classroom assignment details\n\nexport const getClassroomAssignmentQuestions = data => {\n  return POST(endpoint + `/classroom/get_assignment_questions`, data);\n};\nexport const addClassroomAssignmentQuestion = data => {\n  // for (let pair of data.entries()) {\n  //   console.log('addClassroomAssignmentQuestion',pair[0] + ': ', pair[1]);\n  // }\n\n  // Check if data is FormData and if response_type is text\n  if (data instanceof FormData) {\n    // Get response_type from FormData\n    const questionType = data.get(\"question_type\");\n    // console.log('responseType--------------------------------------',responseType)\n\n    if (questionType === \"text\") {\n      // console.log('Using POST for text response type')\n      return POST(endpoint + `/classroom/add_assignment_question`, data);\n    } else {\n      console.log(\"Using UploadFormData for non-text response type\");\n      return UPLOAD_FILE(endpoint + `/classroom/add_assignment_question`, data);\n    }\n  } else {\n    // console.log('Using POST for non-FormData')\n    return POST(endpoint + `/classroom/add_assignment_question`, data);\n  }\n};\nexport const updateClassroomAssignmentQuestion = data => {\n  for (let pair of data.entries()) {\n    console.log(\"------------------------------------------\", pair[0] + \": \", pair[1]);\n  }\n  if (data instanceof FormData) {\n    // Get response_type from FormData\n    const questionType = data.get(\"question_type\");\n    const responseType = data.get(\"response_type\");\n    console.log(\"questionType--------------------------------------\", questionType);\n    console.log(\"responseType --------------------------------------\", responseType);\n    if (questionType === \"text\") {\n      // console.log('Using POST for text response type')\n      return POST(endpoint + `/classroom/update_assignment_question`, data);\n    } else {\n      // console.log('Using UploadFormData for non-text response type')\n      return UPLOAD_FILE(endpoint + `/classroom/update_assignment_question`, data);\n    }\n  } else {\n    // console.log('Using PUT for non-FormData')\n    return POST(endpoint + `/classroom/update_assignment_question`, data);\n  }\n};\nexport const deleteClassroomAssignmentQuestion = data => {\n  return POST(endpoint + `/classroom/delete_assignment_question`, data);\n};\nexport const getClassroomAssignmentSubmissions = data => {\n  return POST(endpoint + `/classroom/get_assignment_submissions`, data);\n};\nexport const gradeClassroomAssignmentSubmission = data => {\n  return POST(endpoint + `/classroom/grade_assignment`, data);\n};\nexport const getClassroomAssignmentDetails = data => {\n  return POST(endpoint + `/classroom/get_class_assignment`, data);\n};\nexport const userAssignmentStatus = data => {\n  return POST(endpoint + `/classroom/user_assignment_status`, data);\n};\n\n//   ------------------------------------------------------------------- classroom assessment\n\n// // --Get classroom data api\n// export const getClassroomDetails = (data) => {\n//     return POST(endpoint + `/classroom/get_classes`, data);\n//   };\n\n// -- Get All Assessment to clssroom\nexport const getAllAssessmentToClassroom = data => {\n  return POST(endpoint + `/classroom/assessments/all`, data);\n};\n\n// add assessment to classroom\nexport const addAssessmentToClassroom = data => {\n  return POST(endpoint + `/classroom/assessment/add`, data);\n};\n\n// add assessment to classroom\nexport const removeAssessmentFromClassroom = data => {\n  return POST(endpoint + `/classroom/assessment`, data);\n};\n\n// update assessment\nexport const updateAssessmentInClassroom = data => {\n  return PUT(endpoint + `/classroom/assessment`, data);\n};\n\n//--- Copy assesss to one class to another\nexport const copyAssessment = data => {\n  return POST(endpoint + `/classroom/assessment/copy`, data);\n};\n\n// -------------------------------------------------------------- question bank\n\n// Question Bank\nexport const getQuestionBank = data => {\n  // Ensure all required parameters are included\n  const payload = {\n    page: data.page || 1,\n    limit: data.limit || 10,\n    search: data.search || \"\"\n  };\n  return POST(endpoint + \"/question_bank/questions\", payload);\n};\nexport const createQuestionBank = data => {\n  return POST(endpoint + \"/question_bank/add_question\", data);\n};\nexport const updateQuestionBank = data => {\n  return PUT(endpoint + \"/question_bank/update_question\", data);\n};\nexport const deleteQuestionBank = data => {\n  return POST(endpoint + \"/question_bank/delete_question\", data);\n};\nexport const getQuestionBankById = data => {\n  return POST(endpoint + \"/question_bank/get_question_by_id\", data);\n};\nexport const getTags = () => {\n  return GET(endpoint + \"/question_tags\");\n};\n\n// /delete_question_tag\nexport const deleteQuestionTag = data => {\n  return POST(endpoint + \"/delete_question_tag\", data);\n};\n\n// ------------------------------------------------------------------ asssesment details\n\nexport const addQuestionsToAssessment = data => {\n  return POST(endpoint + `/classroom/assessment/add_questions`, data);\n};\nexport const getQuestionsInAssessmentForAddModule = data => {\n  return POST(endpoint + `/classroom/assessment/available-questions`, data);\n};\nexport const getAllQuestionsForClassroomAssessment = data => {\n  return POST(endpoint + `/classroom/assessment/questions/get`, data);\n};\nexport const removeQuestionFromAssessment = data => {\n  return POST(endpoint + `/classroom/assessment/remove_question`, data);\n};\n\n// --------------------- classroom assessment analytics\n\nexport const getAssessmentResultsInClassroom = data => {\n  return POST(endpoint + `/classroom/assessment/trainee-results`, data);\n};\nexport const getSpecificAssessmentResultInClassroom = data => {\n  return POST(endpoint + `/classroom/assessment/trainee-result`, data);\n};\nexport const getAssessmentAnalytics = data => {\n  return GET(endpoint + `/classroom/${data.class_id}/assessment/${data.assessment_id}/analytics`);\n};\n\n// -------------------------------------------------------- Settings About Organization\n\n// Get organization information with pagination and search\nexport const getAboutOrganization = data => {\n  return POST(endpoint + `/about-organization`, data);\n};\n\n// Add new organization information\nexport const addAboutOrganization = data => {\n  return POST(endpoint + `/about-organization/add`, data);\n};\n\n// Update organization information\nexport const updateAboutOrganization = (id, data) => {\n  // Ensure id is included in both URL and body\n  const updateData = {\n    id: id,\n    name: data.name,\n    description: data.description\n  };\n  return PUT(endpoint + `/about-organization/${id}`, updateData);\n};\n\n// Update organization information status (active/inactive)\nexport const updateAboutOrganizationStatus = (id, data) => {\n  return PUT(endpoint + `/about-organization/status/${id}`, data);\n};\n\n// Delete organization information (soft delete)\nexport const deleteAboutOrganization = id => {\n  return DELETE(endpoint + `/about-organization/${id}`);\n};\n\n// ------------------------------------------------------------- setting FAQ\n\n// Get FAQs with pagination and search\nexport const getFAQs = data => {\n  return POST(endpoint + `/faqs`, data);\n};\n\n// Add new FAQ\nexport const addFAQ = data => {\n  return POST(endpoint + `/faqs/add`, data);\n};\n\n// Update FAQ\nexport const updateFAQ = (id, data) => {\n  return PUT(endpoint + `/faqs/${id}`, data);\n};\n\n// Update FAQ status (active/inactive)\nexport const updateFAQStatus = (id, data) => {\n  return PUT(endpoint + `/faqs/status/${id}`, data);\n};\n\n// Delete FAQ (soft delete)\nexport const deleteFAQ = id => {\n  return DELETE(endpoint + `/faqs/${id}`);\n};\n\n// ------------------- setting payment\n\n// Get payments with pagination, search and status filter\nexport const getPayments = data => {\n  return POST(endpoint + `/payments`, data);\n};\n\n// Get payment details by ID\nexport const getPaymentById = id => {\n  return GET(endpoint + `/payments/${id}`);\n};\n\n// ------------------- setting tickets\n\n// Get tickets with pagination, search and status filter\nexport const getTickets = data => {\n  return POST(endpoint + `/tickets`, data);\n};\n\n// Send ticket response and update status\nexport const sendTicketResponse = (id, data) => {\n  return PUT(endpoint + `/tickets/${id}/response`, data);\n};\n\n// -------------------------------------------- Announcement \n\n// Create a API endpoint for get Announcements endpoint\nexport const getAnnouncements = params => {\n  return GET(endpoint + `/announcement`, params);\n};\nexport const getAnnouncementsRole = () => {\n  return GET(endpoint + `/get_announcment_role`);\n};\n// create a API endpoint for creating announcements\nexport const createAnnouncements = data => {\n  return POST(endpoint + `/announcement`, data);\n};\n// create a API endpoint for updating announcements\nexport const updateAnnouncements = (announcement_id, data) => {\n  return PUT(endpoint + `/announcements/${announcement_id}`, data);\n};\n// create a API endpoint for deleting announcements\nexport const deleteAnnouncement = announcement_id => {\n  return DELETE(endpoint + `/announcements/${announcement_id}`);\n};\n// Send Announcements through firebase\nexport const sendAnnouncements = data => {\n  return POST(endpoint + `/send_announcements`, data);\n};\n\n// ------------------------------------------------------- certificates \n\nexport const getOrgCertificate = data => {\n  return GET(endpoint + `/get_certificate?page=${data.page}&limit=${data.limit}`);\n};\n\n// create a API endpoint for creating certificates\nexport const createCertificate = data => {\n  const url = endpoint + `/create_certificate`;\n  console.log('Creating certificate with URL:', url);\n  console.log('FormData being sent:', data);\n  return UPLOAD_FILE(url, data);\n};\n\n// create a API endpoint for deleting certificates\nexport const deleteCertificateTemplate = certificate_id => {\n  return DELETE(endpoint + `/certificate_template/${certificate_id}`);\n};\nexport const editCertificate = data => {\n  return POST(endpoint + `/edit_certificate`, data);\n};\n\n// ------------------------------------------------------------- roles and access\n\n// ---------------------- Users \n\nexport const getOrgUsers = data => {\n  return GET(endpoint + `/orgUser?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`);\n};\nexport const deleteUser = async user_id => {\n  return DELETE(endpoint + `/delete_user/${user_id}`);\n};\nexport const toggleUserStatus = (user_id, status) => {\n  return PUT(endpoint + `/deactivate_user/${user_id}`, {\n    status\n  });\n};\nexport const createAdminUser = data => {\n  return UPLOAD_FILE(endpoint + `/create-admin-user`, data);\n};\nexport const editOrganisationUser = (data, userId) => {\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/editOrgUser/${userId}`, data, 'PUT');\n};\n\n// --------------------------------------- roles and permissions \n\n// Roles and Permissions\nexport const getRolesAndPermissions = data => {\n  return GET(endpoint + `/roles?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`);\n};\nexport const activateRole = role_id => {\n  return PUT(endpoint + `/roles/active/${role_id}`);\n};\nexport const InactiveRole = role_id => {\n  return PUT(endpoint + `/roles/deactive/${role_id}`);\n};\n_c3 = InactiveRole;\nexport const deleteRole = async role_id => {\n  return DELETE(endpoint + `/roles/${role_id}`);\n};\nexport const createPermissions = data => {\n  return POST(endpoint + `/create_role`, data);\n};\nexport const getPermissions = () => {\n  return GET(endpoint + `/get_permissions`);\n};\nexport const getRoleDetails = roleId => {\n  return GET(endpoint + `/roles/${roleId}/permissions`);\n};\nexport const updateRoleAndPermissions = (roleId, data) => {\n  return PUT(endpoint + `/roles/${roleId}`, data);\n};\n\n// /create_full_module_hierarchy\nexport const createFullModuleHierarchy = data => {\n  return POST(endpoint + `/create_full_module_hierarchy`, data);\n};\n\n// /get_permissions_by_role_id\nexport const getPermissionsByRoleId = () => {\n  return GET(endpoint + `/get_permissions_by_role_id`);\n};\n\n// -------new \n\n// /get_full_module_hierarchy\nexport const getFullModulegetFullModuleHierarchyWithActionsHierarchy = () => {\n  return GET(endpoint + `/get_full_module_hierarchy`);\n};\n\n// --------------------------------- Zoom Meeting SDK Live Classes\n\n// Create a new live class with Zoom meeting\nexport const createZoomLiveClass = data => {\n  return POST(endpoint + `/classroom/zoom_live_class`, data);\n};\n\n// Get all live classes for a classroom\nexport const getZoomLiveClasses = data => {\n  return POST(endpoint + `/classroom/zoom_live_classes`, data);\n};\n\n// Update a live class\nexport const updateZoomLiveClass = (liveClassId, data) => {\n  return PUT(endpoint + `/classroom/zoom_live_class/${liveClassId}`, data);\n};\n\n// Delete a live class\nexport const deleteZoomLiveClass = liveClassId => {\n  return DELETE(endpoint + `/classroom/zoom_live_class/${liveClassId}`);\n};\n\n// Start a live class meeting\nexport const startZoomLiveClass = data => {\n  return POST(endpoint + `/classroom/zoom_live_class/start`, data);\n};\n\n// End a live class meeting\nexport const endZoomLiveClass = data => {\n  return POST(endpoint + `/classroom/zoom_live_class/end`, data);\n};\n\n// Generate join token for trainee\nexport const generateJoinToken = data => {\n  return POST(endpoint + `/classroom/zoom_live_class/join_token`, data);\n};\n\n// updateRolePermission \nexport const updateRolePermission = data => {\n  return POST(endpoint + `/update_role_permission`, data);\n};\n\n//insertRoleAndPermissions\nexport const insertRoleAndPermissions = data => {\n  return POST(endpoint + `/insert_role_and_permissions`, data);\n};\n\n// deleteRoleAndPermissions\nexport const deleteRoleAndPermissions = data => {\n  return POST(endpoint + `/delete_role_and_permissions`, data);\n};\n\n// ------------------------------ live_Classroom \n\n// /create_live_class\nexport const createLiveClass = data => {\n  return POST(endpoint + `/create_live_class`, data);\n};\n\n// Get all live classes\nexport const getLiveClasses = data => {\n  return POST(endpoint + `/get_live_classes`, data);\n};\n\n// Start live class\nexport const startLiveClass = data => {\n  return POST(endpoint + `/start_live_class`, data);\n};\n\n// End live class\nexport const endLiveClass = data => {\n  return POST(endpoint + `/end_live_class`, data);\n};\n\n// Delete live class\nexport const deleteLiveClass = data => {\n  return POST(endpoint + `/delete_live_class`, data);\n};\nexport const updateLiveClass = data => {\n  return PUT(endpoint + `/update-live-class`, data);\n};\n\n// add recourses  \nexport const createClassroomResource = data => {\n  return UPLOAD_FILE(endpoint + `/create_classroom_resource`, data);\n};\nexport const getClassroomResources = data => {\n  return POST(endpoint + `/get_classroom_resources`, data);\n};\nexport const updateClassroomResource = data => {\n  return UPLOAD_FILE(endpoint + `/update_classroom_resource`, data);\n};\nexport const deleteClassroomResource = data => {\n  return POST(endpoint + `/delete_classroom_resource`, data);\n};\nexport const toggleResourceStatus = data => {\n  return POST(endpoint + `/toggle_resource_status`, data);\n};\n\n// -------------------------------------------------------------------------- organization \nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EditVideoContent\");\n$RefreshReg$(_c2, \"RejectApprovalCourse\");\n$RefreshReg$(_c3, \"InactiveRole\");", "map": {"version": 3, "names": ["DELETE", "GET", "POST", "PUT", "UPLOAD_FILE", "UPLOAD_FILE_WITH_METHOD", "endpoint", "getDashboardData", "getTraineeAnalytics", "data", "courses_api", "coursePerformance", "courseId", "coursePerformanceDetails", "course_id", "search", "page", "limit", "status", "getCourseSurveys", "getCourseAssessments", "getAssessmentDetails", "assessmentId", "getAssessmentAnalyticsUsingGemini", "singleSurveyDetails", "courseUserActivity", "courseDelete", "sendApprovalCourse", "cancelApprovalCourse", "getCourseModule", "id", "createModule", "deleteModule", "module_id", "updateModule", "moduleId", "moduleStatus", "getCourseModuleContent", "createAssessmentAPI", "createDocument", "createVideoAPI", "deleteContentById", "content_id", "type", "changeContentstatus", "params", "updateContentOrder", "getVideoDetailsById", "video_id", "getCourseReviews", "queryParams", "EditVideoContent", "_c", "editDocument", "getDocumentById", "doc_id", "getModuleQuestions", "assessment_id", "getCourseVideos", "moduleParam", "addQuestion", "response", "FormData", "console", "log", "warn", "success", "message", "undefined", "error", "toString", "deleteQuestion", "question_id", "editQuestion", "questionId", "get", "editAssessment", "getCourseCertificate", "createNewCourse", "getCourseDetailsById", "updateCourseDetails", "getCourseList", "getCourseApprovalANDApprovedANDRejected", "approveCourse", "RejectApprovalCourse", "rejected_note", "reason", "_c2", "classroomDashboard", "classroomVisibility", "getClassroomDetails", "getAllTrainers", "createClassroom", "updateClassroomDetails", "updateClassroomStatus", "deleteClassroom", "classroom_id", "getTrainees", "removeTraineeFromClassroom", "user_id", "class_id", "getAllUsersExceptClassroom", "addTraineesToClassroom", "deleteMultipleTrainees", "getAssignmentsForClassroom", "addClassroomAssignment", "deleteClassroomAssignment", "updateClassroomAssignment", "getClassroomAssignmentQuestions", "addClassroomAssignmentQuestion", "questionType", "updateClassroomAssignmentQuestion", "pair", "entries", "responseType", "deleteClassroomAssignmentQuestion", "getClassroomAssignmentSubmissions", "gradeClassroomAssignmentSubmission", "getClassroomAssignmentDetails", "userAssignmentStatus", "getAllAssessmentToClassroom", "addAssessmentToClassroom", "removeAssessmentFromClassroom", "updateAssessmentInClassroom", "copyAssessment", "getQuestionBank", "payload", "createQuestionBank", "updateQuestionBank", "deleteQuestionBank", "getQuestionBankById", "getTags", "deleteQuestionTag", "addQuestionsToAssessment", "getQuestionsInAssessmentForAddModule", "getAllQuestionsForClassroomAssessment", "removeQuestionFromAssessment", "getAssessmentResultsInClassroom", "getSpecificAssessmentResultInClassroom", "getAssessmentAnalytics", "getAboutOrganization", "addAboutOrganization", "updateAboutOrganization", "updateData", "name", "description", "updateAboutOrganizationStatus", "deleteAboutOrganization", "getFAQs", "addFAQ", "updateFAQ", "updateFAQStatus", "deleteFAQ", "getPayments", "getPaymentById", "getTickets", "sendTicketResponse", "getAnnouncements", "getAnnouncementsRole", "createAnnouncements", "updateAnnouncements", "announcement_id", "deleteAnnouncement", "sendAnnouncements", "getOrgCertificate", "createCertificate", "url", "deleteCertificateTemplate", "certificate_id", "editCertificate", "getOrgUsers", "state", "deleteUser", "toggleUserStatus", "createAdminUser", "editOrganisationUser", "userId", "getRolesAndPermissions", "activateRole", "role_id", "InactiveRole", "_c3", "deleteRole", "createPermissions", "getPermissions", "getRoleDetails", "roleId", "updateRoleAndPermissions", "createFullModuleHierarchy", "getPermissionsByRoleId", "getFullModulegetFullModuleHierarchyWithActionsHierarchy", "createZoomLiveClass", "getZoomLiveClasses", "updateZoomLiveClass", "liveClassId", "deleteZoomLiveClass", "startZoomLiveClass", "endZoomLiveClass", "generateJoinToken", "updateRolePermission", "insertRoleAndPermissions", "deleteRoleAndPermissions", "createLiveClass", "getLiveClasses", "startLiveClass", "endLiveClass", "deleteLiveClass", "updateLiveClass", "createClassroomResource", "getClassroomResources", "updateClassroomResource", "deleteClassroomResource", "toggleResourceStatus", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/adminService.js"], "sourcesContent": ["import {\r\n  DELETE,\r\n  GET,\r\n  POST,\r\n  PUT,\r\n  UPLOAD_FILE,\r\n  UPLOAD_FILE_WITH_METHOD,\r\n} from \"./apiController\";\r\n\r\nconst endpoint = \"/mainOrg\";\r\n\r\n// --------------------------------- Dashboard\r\n\r\n// /get_dashboard_data\r\nexport const getDashboardData = () => {\r\n  return GET(endpoint + `/get_dashboard_data`);\r\n};\r\n\r\n// /trainee_analytics\r\nexport const getTraineeAnalytics = (data) => {\r\n  return POST(endpoint + `/trainee_analytics`, data);\r\n};\r\n\r\n// --------------------------------- Prifle\r\n\r\n// ---------------------------------- Courses\r\n\r\n// get all courses\r\nexport const courses_api = () => {\r\n  return GET(endpoint + `/courses_api`);\r\n};\r\n\r\n// ---------------------------------- Course Analytics\r\n\r\n// get course performance stats (enrollments, completed, in progress, etc.)\r\nexport const coursePerformance = (courseId) => {\r\n  return GET(endpoint + `/course_stats/${courseId}`);\r\n};\r\n\r\n// get course performance details with pagination and filters\r\nexport const coursePerformanceDetails = (data) => {\r\n  return GET(\r\n    endpoint +\r\n    `/course_user_details/${data.course_id}?search=${data.search}&page=${data.page}&limit=${data.limit}&status=${data.status}`\r\n  );\r\n};\r\n\r\n// get course surveys\r\nexport const getCourseSurveys = (data) => {\r\n  return POST(endpoint + `/course_surveys`, data);\r\n};\r\n\r\n// get course assessments\r\nexport const getCourseAssessments = (data) => {\r\n  return POST(endpoint + `/course_assessments`, data);\r\n};\r\n\r\n// get assessment details\r\nexport const getAssessmentDetails = (assessmentId) => {\r\n  return GET(endpoint + `/assessment_details/${assessmentId}`);\r\n};\r\n\r\n//get_assessment_analytics_using_gemini\r\nexport const getAssessmentAnalyticsUsingGemini = (data) => {\r\n  return POST(\"/get_assessment_analytics_using_gemini\", data);\r\n};\r\n\r\n\r\n// get single survey details\r\nexport const singleSurveyDetails = (data) => {\r\n  return POST(endpoint + `/single_survey_details`, data);\r\n};\r\n\r\n// get course user activity\r\nexport const courseUserActivity = (courseId) => {\r\n  return GET(endpoint + `/course_activity/${courseId}`);\r\n};\r\n\r\n// delete course\r\nexport const courseDelete = (data) => {\r\n  return DELETE(endpoint + `/course/${data.course_id}`);\r\n};\r\n\r\n// send approval course\r\nexport const sendApprovalCourse = (data) => {\r\n  return POST(endpoint + `/send_approval_course`, data);\r\n};\r\n\r\n// cancel approval course\r\nexport const cancelApprovalCourse = (data) => {\r\n  return POST(endpoint + `/cancel_approval_course`, data);\r\n};\r\n\r\n// get course module\r\nexport const getCourseModule = (data) => {\r\n  return GET(endpoint + `/modules/${data.id}?search=${data.search || \"\"}`);\r\n};\r\n\r\n// create module\r\nexport const createModule = (data) => {\r\n  return POST(endpoint + `/module/new`, data);\r\n};\r\n\r\n// delete module\r\nexport const deleteModule = async (data) => {\r\n  return DELETE(\r\n    endpoint + `/course/${data.course_id}/module/${data.module_id}`\r\n  );\r\n};\r\n\r\n// update module\r\nexport const updateModule = (moduleId, data) => {\r\n  return PUT(endpoint + `/module/${moduleId}`, data);\r\n};\r\n\r\n// update module status\r\nexport const moduleStatus = (data) => {\r\n  return PUT(endpoint + `/course/${data.course_id}/module/${data.module_id}`, {\r\n    status: data.status,\r\n  });\r\n};\r\n\r\n// get module content\r\nexport const getCourseModuleContent = (data) => {\r\n  return GET(endpoint + `/modules-content/${data.module_id}`);\r\n};\r\n\r\n// create assessment\r\nexport const createAssessmentAPI = (data) => {\r\n  return POST(endpoint + `/assessment`, data); // Calls the backend API to create an assessment\r\n};\r\n\r\nexport const createDocument = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/document`, data);\r\n};\r\n\r\nexport const createVideoAPI = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/video/upload`, data);\r\n};\r\n\r\nexport const deleteContentById = async (data) => {\r\n  return DELETE(endpoint + `/module-content/${data.content_id}/${data.type}`);\r\n};\r\n\r\nexport const changeContentstatus = (params, data) => {\r\n  return PUT(\r\n    endpoint +\r\n    `/modules-content-active-deactive/${params.module_id}/${params.content_id}`,\r\n    data\r\n  );\r\n};\r\n\r\n// update content order\r\nexport const updateContentOrder = (data) => {\r\n  return POST(endpoint + `/content-order-update`, data);\r\n};\r\n\r\nexport const getVideoDetailsById = (data) => {\r\n  return GET(\r\n    endpoint + `/get_video_details/${data.video_id}/${data.module_id}`\r\n  );\r\n};\r\n\r\nexport const getCourseReviews = (course_id, video_id) => {\r\n  const queryParams = video_id\r\n    ? `course_id=${course_id}&video_id=${video_id}`\r\n    : `course_id=${course_id}`;\r\n  return GET(`/org/get_course_reviews?${queryParams}`);\r\n};\r\n\r\nexport const EditVideoContent = (data) => {\r\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/edit-video`, data, \"PUT\");\r\n};\r\n\r\nexport const editDocument = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/course/edit-document`, data); // Use POST method as defined in backend route\r\n};\r\n\r\n//   get_document/:doc_i\r\nexport const getDocumentById = (data) => {\r\n  return GET(endpoint + `/get_document/${data.doc_id}`);\r\n};\r\n\r\n// get module questions\r\nexport const getModuleQuestions = (data) => {\r\n  return GET(\r\n    endpoint + `/course/get-questions/${data.module_id}/${data.assessment_id}`\r\n  );\r\n};\r\n\r\nexport const getCourseVideos = (data) => {\r\n  const moduleParam = data.module_id ? `/${data.module_id}` : \"\";\r\n  return GET(endpoint + `/course/videos/${data.course_id}${moduleParam}`);\r\n};\r\n\r\nexport const addQuestion = async (data) => {\r\n  try {\r\n    // If data is FormData, use it directly; otherwise use POST\r\n    let response;\r\n    if (data instanceof FormData) {\r\n      console.log(\"Using UploadFormData for form data\");\r\n      response = await UPLOAD_FILE(endpoint + `/course/add-questions`, data);\r\n      console.log(\"Response from UploadFormData:\", response);\r\n    } else {\r\n      console.log(\"Using POST for JSON data\");\r\n      response = await POST(endpoint + `/course/add-questions`, data);\r\n      console.log(\"Response from POST:\", response);\r\n    }\r\n\r\n    // If response is undefined, create a default error response\r\n    if (!response) {\r\n      console.warn(\r\n        \"Response from server is undefined, creating default response\"\r\n      );\r\n      // Check if the form data was sent successfully despite no response\r\n      return {\r\n        success: true,\r\n        message: \"Question may have been added, but no response was received\",\r\n      };\r\n    }\r\n\r\n    // If we have a response with success property, it's already formatted correctly\r\n    if (response.success !== undefined) {\r\n      console.log(\r\n        \"Response already has success property, returning directly:\",\r\n        response\r\n      );\r\n      return response;\r\n    }\r\n\r\n    // If we get here, we have a response but it's not in the expected format\r\n    console.log(\"Formatting response to expected format:\", response);\r\n    return {\r\n      success: true,\r\n      message: \"Question added successfully\",\r\n      data: response,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in addQuestion:\", error);\r\n    // Return an error object rather than throwing\r\n    return { success: false, error: error.toString() };\r\n  }\r\n};\r\n\r\nexport const deleteQuestion = (data) => {\r\n  return DELETE(endpoint + `/course/delete-question/${data.question_id}`);\r\n};\r\n\r\nexport const editQuestion = (data) => {\r\n  // If data is FormData, use it directly with PUT method; otherwise use PUT\r\n  if (data instanceof FormData) {\r\n    const questionId = data.get(\"id\");\r\n    return UPLOAD_FILE_WITH_METHOD(\r\n      endpoint + `/course/edit-question/${questionId}`,\r\n      data,\r\n      \"PUT\"\r\n    );\r\n  } else {\r\n    const questionId = data.id;\r\n    return PUT(endpoint + `/course/edit-question/${questionId}`, data);\r\n  }\r\n};\r\n\r\nexport const editAssessment = (data) => {\r\n  return PUT(endpoint + `/edit-assessment`, data);\r\n};\r\n\r\n// create course\r\nexport const getCourseCertificate = (search = \"\") => {\r\n  return GET(\r\n    endpoint + `/get_course_certificate${search ? `?search=${search}` : \"\"}`\r\n  );\r\n};\r\n\r\nexport const createNewCourse = (data) => {\r\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/new`, data);\r\n};\r\n\r\n//   /course_details_by_id uign payload\r\nexport const getCourseDetailsById = (data) => {\r\n  return POST(endpoint + `/course_details_by_id`, data);\r\n};\r\n\r\nexport const updateCourseDetails = (courseId, data) => {\r\n  return PUT(endpoint + `/course/update/${courseId}`, data);\r\n};\r\n\r\nexport const getCourseList = (data) => {\r\n  return GET(\r\n    endpoint +\r\n    `/courses?page=${data.page}&limit=${data.limit}&search=${data.search || \"\"\r\n    }&status=${data.status || \"all\"}`\r\n  );\r\n};\r\n\r\n// ---------------------------------------------------- course ApprovalRequest\r\n\r\n// /get_course_categories\r\nexport const getCourseApprovalANDApprovedANDRejected = () => {\r\n  return GET(endpoint + `/course_approval_and_approved_and_rejected`);\r\n};\r\n\r\nexport const approveCourse = (data) => {\r\n  return POST(endpoint + `/approve_course/${data.id}`);\r\n};\r\n\r\nexport const RejectApprovalCourse = (data) => {\r\n  return POST(endpoint + `/course/reject_course`, {\r\n    course_id: data.id,\r\n    rejected_note: data.reason,\r\n  });\r\n};\r\n\r\n//   ------------------------------------------------------------ classroom\r\n\r\nexport const classroomDashboard = (data) => {\r\n  return POST(endpoint + `/classroom/dashboard`, data);\r\n};\r\n\r\n// /classroom/visibility\r\nexport const classroomVisibility = (data) => {\r\n  return POST(endpoint + `/classroom/visibility`, data);\r\n};\r\n\r\n// --Get classroom data api\r\nexport const getClassroomDetails = (data) => {\r\n  return POST(endpoint + `/classroom/get_classes`, data);\r\n};\r\n\r\nexport const getAllTrainers = (data) => {\r\n  return GET(endpoint + `/classroom/get_trainers`);\r\n};\r\n\r\nexport const createClassroom = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/classroom`, data);\r\n};\r\n\r\n// --Update classroom data api\r\nexport const updateClassroomDetails = (data) => {\r\n  return PUT(endpoint + `/classroom`, data);\r\n};\r\n\r\n// -- Update classroom status api\r\nexport const updateClassroomStatus = (data) => {\r\n  return PUT(endpoint + `/classroom/status`, data);\r\n};\r\n\r\n// -- Delete classroom api\r\nexport const deleteClassroom = (data) => {\r\n  return DELETE(endpoint + `/classroom/${data.classroom_id}`);\r\n};\r\n\r\n// -----------------------------------------------  classroom Trainees\r\n\r\n//-- get trainees details api\r\nexport const getTrainees = (data) => {\r\n  return POST(endpoint + `/classroom/trainee`, data);\r\n};\r\n\r\nexport const removeTraineeFromClassroom = ({ user_id, class_id }) => {\r\n  return DELETE(\r\n    endpoint + `/classroom/trainee?user_id=${user_id}&class_id=${class_id}`\r\n  );\r\n};\r\n\r\n// -- get all user from organization\r\nexport const getAllUsersExceptClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/get_all_trainees`, data);\r\n};\r\n\r\n//--- Add multiple trainees to classroom\r\nexport const addTraineesToClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/trainee/add`, data);\r\n};\r\n\r\n//--- Copy assesss to one class to another\r\nexport const deleteMultipleTrainees = (data) => {\r\n  return POST(endpoint + `/classroom/trainee/delete`, data);\r\n};\r\n\r\n//   -------------------------------------------- classroom assessments\r\n\r\nexport const getAssignmentsForClassroom = (data) => {\r\n  return POST(endpoint + \"/classroom/get_assignments\", data);\r\n};\r\n\r\nexport const addClassroomAssignment = (data) => {\r\n  // If data is FormData (contains file), use UploadFormData, otherwise use regular POST\r\n  if (data instanceof FormData) {\r\n    return UPLOAD_FILE(endpoint + `/classroom/create_assignment`, data);\r\n  } else {\r\n    return POST(endpoint + `/classroom/create_assignment`, data);\r\n  }\r\n};\r\n\r\nexport const deleteClassroomAssignment = (data) => {\r\n  return POST(endpoint + `/classroom/delete_assignment`, data);\r\n};\r\n\r\nexport const updateClassroomAssignment = (data) => {\r\n  // If data is FormData (contains file), use UploadFormData with PUT, otherwise use regular PUT\r\n  if (data instanceof FormData) {\r\n    return UPLOAD_FILE(endpoint + `/classroom/update_assignment`, data, \"PUT\");\r\n  } else {\r\n    return PUT(endpoint + `/classroom/update_assignment`, data);\r\n  }\r\n};\r\n\r\n// ------------------------------------------------- classroom assignment details\r\n\r\nexport const getClassroomAssignmentQuestions = (data) => {\r\n  return POST(endpoint + `/classroom/get_assignment_questions`, data);\r\n};\r\n\r\nexport const addClassroomAssignmentQuestion = (data) => {\r\n  // for (let pair of data.entries()) {\r\n  //   console.log('addClassroomAssignmentQuestion',pair[0] + ': ', pair[1]);\r\n  // }\r\n\r\n  // Check if data is FormData and if response_type is text\r\n  if (data instanceof FormData) {\r\n    // Get response_type from FormData\r\n    const questionType = data.get(\"question_type\");\r\n    // console.log('responseType--------------------------------------',responseType)\r\n\r\n    if (questionType === \"text\") {\r\n      // console.log('Using POST for text response type')\r\n      return POST(endpoint + `/classroom/add_assignment_question`, data);\r\n    } else {\r\n      console.log(\"Using UploadFormData for non-text response type\");\r\n      return UPLOAD_FILE(endpoint + `/classroom/add_assignment_question`, data);\r\n    }\r\n  } else {\r\n    // console.log('Using POST for non-FormData')\r\n    return POST(endpoint + `/classroom/add_assignment_question`, data);\r\n  }\r\n};\r\n\r\nexport const updateClassroomAssignmentQuestion = (data) => {\r\n  for (let pair of data.entries()) {\r\n    console.log(\r\n      \"------------------------------------------\",\r\n      pair[0] + \": \",\r\n      pair[1]\r\n    );\r\n  }\r\n\r\n  if (data instanceof FormData) {\r\n    // Get response_type from FormData\r\n    const questionType = data.get(\"question_type\");\r\n    const responseType = data.get(\"response_type\");\r\n    console.log(\r\n      \"questionType--------------------------------------\",\r\n      questionType\r\n    );\r\n    console.log(\r\n      \"responseType --------------------------------------\",\r\n      responseType\r\n    );\r\n\r\n    if (questionType === \"text\") {\r\n      // console.log('Using POST for text response type')\r\n      return POST(endpoint + `/classroom/update_assignment_question`, data);\r\n    } else {\r\n      // console.log('Using UploadFormData for non-text response type')\r\n      return UPLOAD_FILE(\r\n        endpoint + `/classroom/update_assignment_question`,\r\n        data\r\n      );\r\n    }\r\n  } else {\r\n    // console.log('Using PUT for non-FormData')\r\n    return POST(endpoint + `/classroom/update_assignment_question`, data);\r\n  }\r\n};\r\nexport const deleteClassroomAssignmentQuestion = (data) => {\r\n  return POST(endpoint + `/classroom/delete_assignment_question`, data);\r\n};\r\n\r\nexport const getClassroomAssignmentSubmissions = (data) => {\r\n  return POST(endpoint + `/classroom/get_assignment_submissions`, data);\r\n};\r\n\r\nexport const gradeClassroomAssignmentSubmission = (data) => {\r\n  return POST(endpoint + `/classroom/grade_assignment`, data);\r\n};\r\n\r\nexport const getClassroomAssignmentDetails = (data) => {\r\n  return POST(endpoint + `/classroom/get_class_assignment`, data);\r\n};\r\n\r\nexport const userAssignmentStatus = (data) => {\r\n  return POST(endpoint + `/classroom/user_assignment_status`, data);\r\n};\r\n\r\n//   ------------------------------------------------------------------- classroom assessment\r\n\r\n// // --Get classroom data api\r\n// export const getClassroomDetails = (data) => {\r\n//     return POST(endpoint + `/classroom/get_classes`, data);\r\n//   };\r\n\r\n// -- Get All Assessment to clssroom\r\nexport const getAllAssessmentToClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/assessments/all`, data);\r\n};\r\n\r\n// add assessment to classroom\r\nexport const addAssessmentToClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/add`, data);\r\n};\r\n\r\n// add assessment to classroom\r\nexport const removeAssessmentFromClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/assessment`, data);\r\n};\r\n\r\n// update assessment\r\nexport const updateAssessmentInClassroom = (data) => {\r\n  return PUT(endpoint + `/classroom/assessment`, data);\r\n};\r\n\r\n//--- Copy assesss to one class to another\r\nexport const copyAssessment = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/copy`, data);\r\n};\r\n\r\n// -------------------------------------------------------------- question bank\r\n\r\n// Question Bank\r\nexport const getQuestionBank = (data) => {\r\n  // Ensure all required parameters are included\r\n  const payload = {\r\n    page: data.page || 1,\r\n    limit: data.limit || 10,\r\n    search: data.search || \"\",\r\n  };\r\n  return POST(endpoint + \"/question_bank/questions\", payload);\r\n};\r\n\r\nexport const createQuestionBank = (data) => {\r\n  return POST(endpoint + \"/question_bank/add_question\", data);\r\n};\r\n\r\nexport const updateQuestionBank = (data) => {\r\n  return PUT(endpoint + \"/question_bank/update_question\", data);\r\n};\r\n\r\nexport const deleteQuestionBank = (data) => {\r\n  return POST(endpoint + \"/question_bank/delete_question\", data);\r\n};\r\n\r\nexport const getQuestionBankById = (data) => {\r\n  return POST(endpoint + \"/question_bank/get_question_by_id\", data);\r\n};\r\n\r\nexport const getTags = () => {\r\n  return GET(endpoint + \"/question_tags\");\r\n};\r\n\r\n// /delete_question_tag\r\nexport const deleteQuestionTag = (data) => {\r\n  return POST(endpoint + \"/delete_question_tag\", data);\r\n};\r\n\r\n// ------------------------------------------------------------------ asssesment details\r\n\r\nexport const addQuestionsToAssessment = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/add_questions`, data);\r\n};\r\n\r\nexport const getQuestionsInAssessmentForAddModule = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/available-questions`, data);\r\n};\r\n\r\nexport const getAllQuestionsForClassroomAssessment = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/questions/get`, data);\r\n};\r\n\r\nexport const removeQuestionFromAssessment = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/remove_question`, data);\r\n};\r\n\r\n// --------------------- classroom assessment analytics\r\n\r\nexport const getAssessmentResultsInClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/trainee-results`, data);\r\n};\r\n\r\nexport const getSpecificAssessmentResultInClassroom = (data) => {\r\n  return POST(endpoint + `/classroom/assessment/trainee-result`, data);\r\n};\r\n\r\nexport const getAssessmentAnalytics = (data) => {\r\n  return GET(\r\n    endpoint +\r\n    `/classroom/${data.class_id}/assessment/${data.assessment_id}/analytics`\r\n  );\r\n};\r\n\r\n\r\n\r\n// -------------------------------------------------------- Settings About Organization\r\n\r\n// Get organization information with pagination and search\r\nexport const getAboutOrganization = (data) => {\r\n  return POST(endpoint + `/about-organization`, data);\r\n};\r\n\r\n// Add new organization information\r\nexport const addAboutOrganization = (data) => {\r\n  return POST(endpoint + `/about-organization/add`, data);\r\n};\r\n\r\n// Update organization information\r\nexport const updateAboutOrganization = (id, data) => {\r\n  // Ensure id is included in both URL and body\r\n  const updateData = {\r\n    id: id,\r\n    name: data.name,\r\n    description: data.description\r\n  };\r\n  return PUT(endpoint + `/about-organization/${id}`, updateData);\r\n};\r\n\r\n// Update organization information status (active/inactive)\r\nexport const updateAboutOrganizationStatus = (id, data) => {\r\n  return PUT(endpoint + `/about-organization/status/${id}`, data);\r\n};\r\n\r\n// Delete organization information (soft delete)\r\nexport const deleteAboutOrganization = (id) => {\r\n  return DELETE(endpoint + `/about-organization/${id}`);\r\n};\r\n\r\n\r\n\r\n// ------------------------------------------------------------- setting FAQ\r\n\r\n// Get FAQs with pagination and search\r\nexport const getFAQs = (data) => {\r\n  return POST(endpoint + `/faqs`, data);\r\n};\r\n\r\n// Add new FAQ\r\nexport const addFAQ = (data) => {\r\n  return POST(endpoint + `/faqs/add`, data);\r\n};\r\n\r\n// Update FAQ\r\nexport const updateFAQ = (id, data) => {\r\n  return PUT(endpoint + `/faqs/${id}`, data);\r\n};\r\n\r\n// Update FAQ status (active/inactive)\r\nexport const updateFAQStatus = (id, data) => {\r\n  return PUT(endpoint + `/faqs/status/${id}`, data);\r\n};\r\n\r\n// Delete FAQ (soft delete)\r\nexport const deleteFAQ = (id) => {\r\n  return DELETE(endpoint + `/faqs/${id}`);\r\n};\r\n\r\n\r\n\r\n\r\n\r\n\r\n// ------------------- setting payment\r\n\r\n// Get payments with pagination, search and status filter\r\nexport const getPayments = (data) => {\r\n  return POST(endpoint + `/payments`, data);\r\n};\r\n\r\n// Get payment details by ID\r\nexport const getPaymentById = (id) => {\r\n  return GET(endpoint + `/payments/${id}`);\r\n};\r\n\r\n// ------------------- setting tickets\r\n\r\n// Get tickets with pagination, search and status filter\r\nexport const getTickets = (data) => {\r\n  return POST(endpoint + `/tickets`, data);\r\n};\r\n\r\n// Send ticket response and update status\r\nexport const sendTicketResponse = (id, data) => {\r\n  return PUT(endpoint + `/tickets/${id}/response`, data);\r\n};\r\n\r\n// -------------------------------------------- Announcement \r\n\r\n\r\n// Create a API endpoint for get Announcements endpoint\r\nexport const getAnnouncements = (params) => {\r\n  return GET(endpoint + `/announcement`, params);\r\n};\r\n\r\nexport const getAnnouncementsRole = () => {\r\n  return GET(endpoint + `/get_announcment_role`);\r\n};\r\n// create a API endpoint for creating announcements\r\nexport const createAnnouncements = (data) => {\r\n  return POST(endpoint + `/announcement`, data);\r\n};\r\n// create a API endpoint for updating announcements\r\nexport const updateAnnouncements = (announcement_id, data) => {\r\n  return PUT(endpoint + `/announcements/${announcement_id}`, data);\r\n};\r\n// create a API endpoint for deleting announcements\r\nexport const deleteAnnouncement = (announcement_id) => {\r\n  return DELETE(endpoint + `/announcements/${announcement_id}`);\r\n};\r\n// Send Announcements through firebase\r\nexport const sendAnnouncements = (data) => {\r\n  return POST(endpoint + `/send_announcements`, data);\r\n};\r\n\r\n\r\n\r\n\r\n// ------------------------------------------------------- certificates \r\n\r\nexport const getOrgCertificate = (data) => {\r\n  return GET(\r\n    endpoint + `/get_certificate?page=${data.page}&limit=${data.limit}`\r\n  );\r\n};\r\n\r\n// create a API endpoint for creating certificates\r\nexport const createCertificate = (data) => {\r\n  const url = endpoint + `/create_certificate`;\r\n  console.log('Creating certificate with URL:', url);\r\n  console.log('FormData being sent:', data);\r\n  return UPLOAD_FILE(url, data);\r\n};\r\n\r\n// create a API endpoint for deleting certificates\r\nexport const deleteCertificateTemplate = (certificate_id) => {\r\n  return DELETE(endpoint + `/certificate_template/${certificate_id}`);\r\n};\r\n\r\n\r\nexport const editCertificate = (data) => {\r\n  return POST(endpoint + `/edit_certificate`, data);\r\n};\r\n\r\n\r\n\r\n// ------------------------------------------------------------- roles and access\r\n\r\n// ---------------------- Users \r\n\r\nexport const getOrgUsers = (data) => {\r\n  return GET(\r\n    endpoint +\r\n      `/orgUser?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`\r\n  );\r\n};\r\n\r\nexport const deleteUser = async (user_id) => {\r\n  return DELETE(endpoint + `/delete_user/${user_id}`);\r\n};\r\n\r\nexport const toggleUserStatus = (user_id, status) => {\r\n  return PUT(endpoint + `/deactivate_user/${user_id}`, { status });\r\n};\r\n\r\n\r\n\r\n\r\nexport const createAdminUser = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/create-admin-user`, data);\r\n};\r\n\r\nexport const editOrganisationUser = (data, userId) => {\r\n  return UPLOAD_FILE_WITH_METHOD(endpoint + `/editOrgUser/${userId}`, data, 'PUT');\r\n};\r\n\r\n\r\n\r\n// --------------------------------------- roles and permissions \r\n\r\n// Roles and Permissions\r\nexport const getRolesAndPermissions = (data) => {\r\n  return GET(\r\n    endpoint +\r\n      `/roles?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`\r\n  );\r\n};\r\n\r\n\r\nexport const activateRole = (role_id) => {\r\n  return PUT(endpoint + `/roles/active/${role_id}`);\r\n};\r\nexport const InactiveRole = (role_id) => {\r\n  return PUT(endpoint + `/roles/deactive/${role_id}`);\r\n};\r\nexport const deleteRole = async (role_id) => {\r\n  return DELETE(endpoint + `/roles/${role_id}`);\r\n};\r\n\r\n\r\nexport const createPermissions = (data) => {\r\n  return POST(endpoint + `/create_role`, data);\r\n};\r\n\r\nexport const getPermissions = () => {\r\n  return GET(endpoint + `/get_permissions`);\r\n};\r\n\r\nexport const getRoleDetails = (roleId) => {\r\n  return GET(endpoint + `/roles/${roleId}/permissions`);\r\n};\r\n\r\n\r\nexport const updateRoleAndPermissions = (roleId, data) => {\r\n  return PUT(endpoint + `/roles/${roleId}`, data);\r\n};\r\n\r\n\r\n\r\n// /create_full_module_hierarchy\r\nexport const createFullModuleHierarchy = (data) => {\r\n  return POST(endpoint + `/create_full_module_hierarchy`, data);\r\n};\r\n\r\n// /get_permissions_by_role_id\r\nexport const getPermissionsByRoleId = () => {\r\n  return GET(endpoint + `/get_permissions_by_role_id`);\r\n};\r\n\r\n\r\n\r\n// -------new \r\n\r\n// /get_full_module_hierarchy\r\nexport const getFullModulegetFullModuleHierarchyWithActionsHierarchy = () => {\r\n  return GET(endpoint + `/get_full_module_hierarchy`);\r\n};\r\n\r\n// --------------------------------- Zoom Meeting SDK Live Classes\r\n\r\n// Create a new live class with Zoom meeting\r\nexport const createZoomLiveClass = (data) => {\r\n  return POST(endpoint + `/classroom/zoom_live_class`, data);\r\n};\r\n\r\n// Get all live classes for a classroom\r\nexport const getZoomLiveClasses = (data) => {\r\n  return POST(endpoint + `/classroom/zoom_live_classes`, data);\r\n};\r\n\r\n// Update a live class\r\nexport const updateZoomLiveClass = (liveClassId, data) => {\r\n  return PUT(endpoint + `/classroom/zoom_live_class/${liveClassId}`, data);\r\n};\r\n\r\n// Delete a live class\r\nexport const deleteZoomLiveClass = (liveClassId) => {\r\n  return DELETE(endpoint + `/classroom/zoom_live_class/${liveClassId}`);\r\n};\r\n\r\n// Start a live class meeting\r\nexport const startZoomLiveClass = (data) => {\r\n  return POST(endpoint + `/classroom/zoom_live_class/start`, data);\r\n};\r\n\r\n// End a live class meeting\r\nexport const endZoomLiveClass = (data) => {\r\n  return POST(endpoint + `/classroom/zoom_live_class/end`, data);\r\n};\r\n\r\n// Generate join token for trainee\r\nexport const generateJoinToken = (data) => {\r\n  return POST(endpoint + `/classroom/zoom_live_class/join_token`, data);\r\n};\r\n\r\n\r\n// updateRolePermission \r\nexport const updateRolePermission = (data) => {\r\n  return POST(endpoint + `/update_role_permission`, data);\r\n};\r\n\r\n\r\n//insertRoleAndPermissions\r\nexport const insertRoleAndPermissions = (data) => {\r\n  return POST(endpoint + `/insert_role_and_permissions`, data);\r\n};\r\n\r\n// deleteRoleAndPermissions\r\nexport const deleteRoleAndPermissions = (data) => {\r\n  return POST(endpoint + `/delete_role_and_permissions`, data);\r\n};\r\n\r\n\r\n// ------------------------------ live_Classroom \r\n\r\n// /create_live_class\r\nexport const createLiveClass = (data) => {\r\n  return POST(endpoint + `/create_live_class`, data);\r\n};\r\n\r\n// Get all live classes\r\nexport const getLiveClasses = (data) => {\r\n  return POST(endpoint + `/get_live_classes`, data);\r\n};\r\n\r\n// Start live class\r\nexport const startLiveClass = (data) => {\r\n  return POST(endpoint + `/start_live_class`, data);\r\n};\r\n\r\n// End live class\r\nexport const endLiveClass = (data) => {\r\n  return POST(endpoint + `/end_live_class`, data);\r\n};\r\n\r\n// Delete live class\r\nexport const deleteLiveClass = (data) => {\r\n  return POST(endpoint + `/delete_live_class`, data);\r\n};\r\n\r\n\r\n\r\nexport const updateLiveClass = (data) => {\r\n  return PUT(endpoint + `/update-live-class`, data);\r\n};\r\n\r\n\r\n\r\n\r\n// add recourses  \r\nexport const createClassroomResource = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/create_classroom_resource`, data);\r\n};\r\nexport const getClassroomResources = (data) => {\r\n  return POST(endpoint + `/get_classroom_resources`, data);\r\n};\r\nexport const updateClassroomResource = (data) => {\r\n  return UPLOAD_FILE(endpoint + `/update_classroom_resource`, data);\r\n};\r\nexport const deleteClassroomResource = (data) => {\r\n  return POST(endpoint + `/delete_classroom_resource`, data);\r\n};\r\nexport const toggleResourceStatus = (data) => {\r\n  return POST(endpoint + `/toggle_resource_status`, data);\r\n};\r\n\r\n\r\n\r\n// -------------------------------------------------------------------------- organization \r\n\r\n\r\n\r\n"], "mappings": "AAAA,SACEA,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,GAAG,EACHC,WAAW,EACXC,uBAAuB,QAClB,iBAAiB;AAExB,MAAMC,QAAQ,GAAG,UAAU;;AAE3B;;AAEA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EACpC,OAAON,GAAG,CAACK,QAAQ,GAAG,qBAAqB,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAME,mBAAmB,GAAIC,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AACpD,CAAC;;AAED;;AAEA;;AAEA;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,OAAOT,GAAG,CAACK,QAAQ,GAAG,cAAc,CAAC;AACvC,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMK,iBAAiB,GAAIC,QAAQ,IAAK;EAC7C,OAAOX,GAAG,CAACK,QAAQ,GAAG,iBAAiBM,QAAQ,EAAE,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAMC,wBAAwB,GAAIJ,IAAI,IAAK;EAChD,OAAOR,GAAG,CACRK,QAAQ,GACR,wBAAwBG,IAAI,CAACK,SAAS,WAAWL,IAAI,CAACM,MAAM,SAASN,IAAI,CAACO,IAAI,UAAUP,IAAI,CAACQ,KAAK,WAAWR,IAAI,CAACS,MAAM,EAC1H,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAIV,IAAI,IAAK;EACxC,OAAOP,IAAI,CAACI,QAAQ,GAAG,iBAAiB,EAAEG,IAAI,CAAC;AACjD,CAAC;;AAED;AACA,OAAO,MAAMW,oBAAoB,GAAIX,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,qBAAqB,EAAEG,IAAI,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAMY,oBAAoB,GAAIC,YAAY,IAAK;EACpD,OAAOrB,GAAG,CAACK,QAAQ,GAAG,uBAAuBgB,YAAY,EAAE,CAAC;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMC,iCAAiC,GAAId,IAAI,IAAK;EACzD,OAAOP,IAAI,CAAC,wCAAwC,EAAEO,IAAI,CAAC;AAC7D,CAAC;;AAGD;AACA,OAAO,MAAMe,mBAAmB,GAAIf,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,wBAAwB,EAAEG,IAAI,CAAC;AACxD,CAAC;;AAED;AACA,OAAO,MAAMgB,kBAAkB,GAAIb,QAAQ,IAAK;EAC9C,OAAOX,GAAG,CAACK,QAAQ,GAAG,oBAAoBM,QAAQ,EAAE,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMc,YAAY,GAAIjB,IAAI,IAAK;EACpC,OAAOT,MAAM,CAACM,QAAQ,GAAG,WAAWG,IAAI,CAACK,SAAS,EAAE,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMa,kBAAkB,GAAIlB,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMmB,oBAAoB,GAAInB,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,yBAAyB,EAAEG,IAAI,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMoB,eAAe,GAAIpB,IAAI,IAAK;EACvC,OAAOR,GAAG,CAACK,QAAQ,GAAG,YAAYG,IAAI,CAACqB,EAAE,WAAWrB,IAAI,CAACM,MAAM,IAAI,EAAE,EAAE,CAAC;AAC1E,CAAC;;AAED;AACA,OAAO,MAAMgB,YAAY,GAAItB,IAAI,IAAK;EACpC,OAAOP,IAAI,CAACI,QAAQ,GAAG,aAAa,EAAEG,IAAI,CAAC;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMuB,YAAY,GAAG,MAAOvB,IAAI,IAAK;EAC1C,OAAOT,MAAM,CACXM,QAAQ,GAAG,WAAWG,IAAI,CAACK,SAAS,WAAWL,IAAI,CAACwB,SAAS,EAC/D,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAE1B,IAAI,KAAK;EAC9C,OAAON,GAAG,CAACG,QAAQ,GAAG,WAAW6B,QAAQ,EAAE,EAAE1B,IAAI,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAM2B,YAAY,GAAI3B,IAAI,IAAK;EACpC,OAAON,GAAG,CAACG,QAAQ,GAAG,WAAWG,IAAI,CAACK,SAAS,WAAWL,IAAI,CAACwB,SAAS,EAAE,EAAE;IAC1Ef,MAAM,EAAET,IAAI,CAACS;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMmB,sBAAsB,GAAI5B,IAAI,IAAK;EAC9C,OAAOR,GAAG,CAACK,QAAQ,GAAG,oBAAoBG,IAAI,CAACwB,SAAS,EAAE,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMK,mBAAmB,GAAI7B,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,aAAa,EAAEG,IAAI,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,OAAO,MAAM8B,cAAc,GAAI9B,IAAI,IAAK;EACtC,OAAOL,WAAW,CAACE,QAAQ,GAAG,WAAW,EAAEG,IAAI,CAAC;AAClD,CAAC;AAED,OAAO,MAAM+B,cAAc,GAAI/B,IAAI,IAAK;EACtC,OAAOL,WAAW,CAACE,QAAQ,GAAG,eAAe,EAAEG,IAAI,CAAC;AACtD,CAAC;AAED,OAAO,MAAMgC,iBAAiB,GAAG,MAAOhC,IAAI,IAAK;EAC/C,OAAOT,MAAM,CAACM,QAAQ,GAAG,mBAAmBG,IAAI,CAACiC,UAAU,IAAIjC,IAAI,CAACkC,IAAI,EAAE,CAAC;AAC7E,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,MAAM,EAAEpC,IAAI,KAAK;EACnD,OAAON,GAAG,CACRG,QAAQ,GACR,oCAAoCuC,MAAM,CAACZ,SAAS,IAAIY,MAAM,CAACH,UAAU,EAAE,EAC3EjC,IACF,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMqC,kBAAkB,GAAIrC,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACvD,CAAC;AAED,OAAO,MAAMsC,mBAAmB,GAAItC,IAAI,IAAK;EAC3C,OAAOR,GAAG,CACRK,QAAQ,GAAG,sBAAsBG,IAAI,CAACuC,QAAQ,IAAIvC,IAAI,CAACwB,SAAS,EAClE,CAAC;AACH,CAAC;AAED,OAAO,MAAMgB,gBAAgB,GAAGA,CAACnC,SAAS,EAAEkC,QAAQ,KAAK;EACvD,MAAME,WAAW,GAAGF,QAAQ,GACxB,aAAalC,SAAS,aAAakC,QAAQ,EAAE,GAC7C,aAAalC,SAAS,EAAE;EAC5B,OAAOb,GAAG,CAAC,2BAA2BiD,WAAW,EAAE,CAAC;AACtD,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAI1C,IAAI,IAAK;EACxC,OAAOJ,uBAAuB,CAACC,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,EAAE,KAAK,CAAC;AAC9E,CAAC;AAAC2C,EAAA,GAFWD,gBAAgB;AAI7B,OAAO,MAAME,YAAY,GAAI5C,IAAI,IAAK;EACpC,OAAOL,WAAW,CAACE,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC,CAAC,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAM6C,eAAe,GAAI7C,IAAI,IAAK;EACvC,OAAOR,GAAG,CAACK,QAAQ,GAAG,iBAAiBG,IAAI,CAAC8C,MAAM,EAAE,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAI/C,IAAI,IAAK;EAC1C,OAAOR,GAAG,CACRK,QAAQ,GAAG,yBAAyBG,IAAI,CAACwB,SAAS,IAAIxB,IAAI,CAACgD,aAAa,EAC1E,CAAC;AACH,CAAC;AAED,OAAO,MAAMC,eAAe,GAAIjD,IAAI,IAAK;EACvC,MAAMkD,WAAW,GAAGlD,IAAI,CAACwB,SAAS,GAAG,IAAIxB,IAAI,CAACwB,SAAS,EAAE,GAAG,EAAE;EAC9D,OAAOhC,GAAG,CAACK,QAAQ,GAAG,kBAAkBG,IAAI,CAACK,SAAS,GAAG6C,WAAW,EAAE,CAAC;AACzE,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAOnD,IAAI,IAAK;EACzC,IAAI;IACF;IACA,IAAIoD,QAAQ;IACZ,IAAIpD,IAAI,YAAYqD,QAAQ,EAAE;MAC5BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDH,QAAQ,GAAG,MAAMzD,WAAW,CAACE,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;MACtEsD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,QAAQ,CAAC;IACxD,CAAC,MAAM;MACLE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvCH,QAAQ,GAAG,MAAM3D,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;MAC/DsD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,QAAQ,CAAC;IAC9C;;IAEA;IACA,IAAI,CAACA,QAAQ,EAAE;MACbE,OAAO,CAACE,IAAI,CACV,8DACF,CAAC;MACD;MACA,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX,CAAC;IACH;;IAEA;IACA,IAAIN,QAAQ,CAACK,OAAO,KAAKE,SAAS,EAAE;MAClCL,OAAO,CAACC,GAAG,CACT,4DAA4D,EAC5DH,QACF,CAAC;MACD,OAAOA,QAAQ;IACjB;;IAEA;IACAE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,QAAQ,CAAC;IAChE,OAAO;MACLK,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,6BAA6B;MACtC1D,IAAI,EAAEoD;IACR,CAAC;EACH,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C;IACA,OAAO;MAAEH,OAAO,EAAE,KAAK;MAAEG,KAAK,EAAEA,KAAK,CAACC,QAAQ,CAAC;IAAE,CAAC;EACpD;AACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAI9D,IAAI,IAAK;EACtC,OAAOT,MAAM,CAACM,QAAQ,GAAG,2BAA2BG,IAAI,CAAC+D,WAAW,EAAE,CAAC;AACzE,CAAC;AAED,OAAO,MAAMC,YAAY,GAAIhE,IAAI,IAAK;EACpC;EACA,IAAIA,IAAI,YAAYqD,QAAQ,EAAE;IAC5B,MAAMY,UAAU,GAAGjE,IAAI,CAACkE,GAAG,CAAC,IAAI,CAAC;IACjC,OAAOtE,uBAAuB,CAC5BC,QAAQ,GAAG,yBAAyBoE,UAAU,EAAE,EAChDjE,IAAI,EACJ,KACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMiE,UAAU,GAAGjE,IAAI,CAACqB,EAAE;IAC1B,OAAO3B,GAAG,CAACG,QAAQ,GAAG,yBAAyBoE,UAAU,EAAE,EAAEjE,IAAI,CAAC;EACpE;AACF,CAAC;AAED,OAAO,MAAMmE,cAAc,GAAInE,IAAI,IAAK;EACtC,OAAON,GAAG,CAACG,QAAQ,GAAG,kBAAkB,EAAEG,IAAI,CAAC;AACjD,CAAC;;AAED;AACA,OAAO,MAAMoE,oBAAoB,GAAGA,CAAC9D,MAAM,GAAG,EAAE,KAAK;EACnD,OAAOd,GAAG,CACRK,QAAQ,GAAG,0BAA0BS,MAAM,GAAG,WAAWA,MAAM,EAAE,GAAG,EAAE,EACxE,CAAC;AACH,CAAC;AAED,OAAO,MAAM+D,eAAe,GAAIrE,IAAI,IAAK;EACvC,OAAOJ,uBAAuB,CAACC,QAAQ,GAAG,aAAa,EAAEG,IAAI,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAMsE,oBAAoB,GAAItE,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACvD,CAAC;AAED,OAAO,MAAMuE,mBAAmB,GAAGA,CAACpE,QAAQ,EAAEH,IAAI,KAAK;EACrD,OAAON,GAAG,CAACG,QAAQ,GAAG,kBAAkBM,QAAQ,EAAE,EAAEH,IAAI,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMwE,aAAa,GAAIxE,IAAI,IAAK;EACrC,OAAOR,GAAG,CACRK,QAAQ,GACR,iBAAiBG,IAAI,CAACO,IAAI,UAAUP,IAAI,CAACQ,KAAK,WAAWR,IAAI,CAACM,MAAM,IAAI,EAAE,WAC/DN,IAAI,CAACS,MAAM,IAAI,KAAK,EACjC,CAAC;AACH,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMgE,uCAAuC,GAAGA,CAAA,KAAM;EAC3D,OAAOjF,GAAG,CAACK,QAAQ,GAAG,4CAA4C,CAAC;AACrE,CAAC;AAED,OAAO,MAAM6E,aAAa,GAAI1E,IAAI,IAAK;EACrC,OAAOP,IAAI,CAACI,QAAQ,GAAG,mBAAmBG,IAAI,CAACqB,EAAE,EAAE,CAAC;AACtD,CAAC;AAED,OAAO,MAAMsD,oBAAoB,GAAI3E,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAE;IAC9CQ,SAAS,EAAEL,IAAI,CAACqB,EAAE;IAClBuD,aAAa,EAAE5E,IAAI,CAAC6E;EACtB,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAC,GAAA,GAPaH,oBAAoB;AASjC,OAAO,MAAMI,kBAAkB,GAAI/E,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,sBAAsB,EAAEG,IAAI,CAAC;AACtD,CAAC;;AAED;AACA,OAAO,MAAMgF,mBAAmB,GAAIhF,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMiF,mBAAmB,GAAIjF,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,wBAAwB,EAAEG,IAAI,CAAC;AACxD,CAAC;AAED,OAAO,MAAMkF,cAAc,GAAIlF,IAAI,IAAK;EACtC,OAAOR,GAAG,CAACK,QAAQ,GAAG,yBAAyB,CAAC;AAClD,CAAC;AAED,OAAO,MAAMsF,eAAe,GAAInF,IAAI,IAAK;EACvC,OAAOL,WAAW,CAACE,QAAQ,GAAG,YAAY,EAAEG,IAAI,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMoF,sBAAsB,GAAIpF,IAAI,IAAK;EAC9C,OAAON,GAAG,CAACG,QAAQ,GAAG,YAAY,EAAEG,IAAI,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMqF,qBAAqB,GAAIrF,IAAI,IAAK;EAC7C,OAAON,GAAG,CAACG,QAAQ,GAAG,mBAAmB,EAAEG,IAAI,CAAC;AAClD,CAAC;;AAED;AACA,OAAO,MAAMsF,eAAe,GAAItF,IAAI,IAAK;EACvC,OAAOT,MAAM,CAACM,QAAQ,GAAG,cAAcG,IAAI,CAACuF,YAAY,EAAE,CAAC;AAC7D,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMC,WAAW,GAAIxF,IAAI,IAAK;EACnC,OAAOP,IAAI,CAACI,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AACpD,CAAC;AAED,OAAO,MAAMyF,0BAA0B,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EACnE,OAAOpG,MAAM,CACXM,QAAQ,GAAG,8BAA8B6F,OAAO,aAAaC,QAAQ,EACvE,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMC,0BAA0B,GAAI5F,IAAI,IAAK;EAClD,OAAOP,IAAI,CAACI,QAAQ,GAAG,6BAA6B,EAAEG,IAAI,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAM6F,sBAAsB,GAAI7F,IAAI,IAAK;EAC9C,OAAOP,IAAI,CAACI,QAAQ,GAAG,wBAAwB,EAAEG,IAAI,CAAC;AACxD,CAAC;;AAED;AACA,OAAO,MAAM8F,sBAAsB,GAAI9F,IAAI,IAAK;EAC9C,OAAOP,IAAI,CAACI,QAAQ,GAAG,2BAA2B,EAAEG,IAAI,CAAC;AAC3D,CAAC;;AAED;;AAEA,OAAO,MAAM+F,0BAA0B,GAAI/F,IAAI,IAAK;EAClD,OAAOP,IAAI,CAACI,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AAC5D,CAAC;AAED,OAAO,MAAMgG,sBAAsB,GAAIhG,IAAI,IAAK;EAC9C;EACA,IAAIA,IAAI,YAAYqD,QAAQ,EAAE;IAC5B,OAAO1D,WAAW,CAACE,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;EACrE,CAAC,MAAM;IACL,OAAOP,IAAI,CAACI,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;EAC9D;AACF,CAAC;AAED,OAAO,MAAMiG,yBAAyB,GAAIjG,IAAI,IAAK;EACjD,OAAOP,IAAI,CAACI,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;AAC9D,CAAC;AAED,OAAO,MAAMkG,yBAAyB,GAAIlG,IAAI,IAAK;EACjD;EACA,IAAIA,IAAI,YAAYqD,QAAQ,EAAE;IAC5B,OAAO1D,WAAW,CAACE,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,EAAE,KAAK,CAAC;EAC5E,CAAC,MAAM;IACL,OAAON,GAAG,CAACG,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;EAC7D;AACF,CAAC;;AAED;;AAEA,OAAO,MAAMmG,+BAA+B,GAAInG,IAAI,IAAK;EACvD,OAAOP,IAAI,CAACI,QAAQ,GAAG,qCAAqC,EAAEG,IAAI,CAAC;AACrE,CAAC;AAED,OAAO,MAAMoG,8BAA8B,GAAIpG,IAAI,IAAK;EACtD;EACA;EACA;;EAEA;EACA,IAAIA,IAAI,YAAYqD,QAAQ,EAAE;IAC5B;IACA,MAAMgD,YAAY,GAAGrG,IAAI,CAACkE,GAAG,CAAC,eAAe,CAAC;IAC9C;;IAEA,IAAImC,YAAY,KAAK,MAAM,EAAE;MAC3B;MACA,OAAO5G,IAAI,CAACI,QAAQ,GAAG,oCAAoC,EAAEG,IAAI,CAAC;IACpE,CAAC,MAAM;MACLsD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,OAAO5D,WAAW,CAACE,QAAQ,GAAG,oCAAoC,EAAEG,IAAI,CAAC;IAC3E;EACF,CAAC,MAAM;IACL;IACA,OAAOP,IAAI,CAACI,QAAQ,GAAG,oCAAoC,EAAEG,IAAI,CAAC;EACpE;AACF,CAAC;AAED,OAAO,MAAMsG,iCAAiC,GAAItG,IAAI,IAAK;EACzD,KAAK,IAAIuG,IAAI,IAAIvG,IAAI,CAACwG,OAAO,CAAC,CAAC,EAAE;IAC/BlD,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5CgD,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EACdA,IAAI,CAAC,CAAC,CACR,CAAC;EACH;EAEA,IAAIvG,IAAI,YAAYqD,QAAQ,EAAE;IAC5B;IACA,MAAMgD,YAAY,GAAGrG,IAAI,CAACkE,GAAG,CAAC,eAAe,CAAC;IAC9C,MAAMuC,YAAY,GAAGzG,IAAI,CAACkE,GAAG,CAAC,eAAe,CAAC;IAC9CZ,OAAO,CAACC,GAAG,CACT,oDAAoD,EACpD8C,YACF,CAAC;IACD/C,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDkD,YACF,CAAC;IAED,IAAIJ,YAAY,KAAK,MAAM,EAAE;MAC3B;MACA,OAAO5G,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;IACvE,CAAC,MAAM;MACL;MACA,OAAOL,WAAW,CAChBE,QAAQ,GAAG,uCAAuC,EAClDG,IACF,CAAC;IACH;EACF,CAAC,MAAM;IACL;IACA,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;EACvE;AACF,CAAC;AACD,OAAO,MAAM0G,iCAAiC,GAAI1G,IAAI,IAAK;EACzD,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;AACvE,CAAC;AAED,OAAO,MAAM2G,iCAAiC,GAAI3G,IAAI,IAAK;EACzD,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;AACvE,CAAC;AAED,OAAO,MAAM4G,kCAAkC,GAAI5G,IAAI,IAAK;EAC1D,OAAOP,IAAI,CAACI,QAAQ,GAAG,6BAA6B,EAAEG,IAAI,CAAC;AAC7D,CAAC;AAED,OAAO,MAAM6G,6BAA6B,GAAI7G,IAAI,IAAK;EACrD,OAAOP,IAAI,CAACI,QAAQ,GAAG,iCAAiC,EAAEG,IAAI,CAAC;AACjE,CAAC;AAED,OAAO,MAAM8G,oBAAoB,GAAI9G,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,mCAAmC,EAAEG,IAAI,CAAC;AACnE,CAAC;;AAED;;AAEA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAM+G,2BAA2B,GAAI/G,IAAI,IAAK;EACnD,OAAOP,IAAI,CAACI,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AAC5D,CAAC;;AAED;AACA,OAAO,MAAMgH,wBAAwB,GAAIhH,IAAI,IAAK;EAChD,OAAOP,IAAI,CAACI,QAAQ,GAAG,2BAA2B,EAAEG,IAAI,CAAC;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMiH,6BAA6B,GAAIjH,IAAI,IAAK;EACrD,OAAOP,IAAI,CAACI,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMkH,2BAA2B,GAAIlH,IAAI,IAAK;EACnD,OAAON,GAAG,CAACG,QAAQ,GAAG,uBAAuB,EAAEG,IAAI,CAAC;AACtD,CAAC;;AAED;AACA,OAAO,MAAMmH,cAAc,GAAInH,IAAI,IAAK;EACtC,OAAOP,IAAI,CAACI,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AAC5D,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMoH,eAAe,GAAIpH,IAAI,IAAK;EACvC;EACA,MAAMqH,OAAO,GAAG;IACd9G,IAAI,EAAEP,IAAI,CAACO,IAAI,IAAI,CAAC;IACpBC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,EAAE;IACvBF,MAAM,EAAEN,IAAI,CAACM,MAAM,IAAI;EACzB,CAAC;EACD,OAAOb,IAAI,CAACI,QAAQ,GAAG,0BAA0B,EAAEwH,OAAO,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAItH,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,6BAA6B,EAAEG,IAAI,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMuH,kBAAkB,GAAIvH,IAAI,IAAK;EAC1C,OAAON,GAAG,CAACG,QAAQ,GAAG,gCAAgC,EAAEG,IAAI,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMwH,kBAAkB,GAAIxH,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,gCAAgC,EAAEG,IAAI,CAAC;AAChE,CAAC;AAED,OAAO,MAAMyH,mBAAmB,GAAIzH,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,mCAAmC,EAAEG,IAAI,CAAC;AACnE,CAAC;AAED,OAAO,MAAM0H,OAAO,GAAGA,CAAA,KAAM;EAC3B,OAAOlI,GAAG,CAACK,QAAQ,GAAG,gBAAgB,CAAC;AACzC,CAAC;;AAED;AACA,OAAO,MAAM8H,iBAAiB,GAAI3H,IAAI,IAAK;EACzC,OAAOP,IAAI,CAACI,QAAQ,GAAG,sBAAsB,EAAEG,IAAI,CAAC;AACtD,CAAC;;AAED;;AAEA,OAAO,MAAM4H,wBAAwB,GAAI5H,IAAI,IAAK;EAChD,OAAOP,IAAI,CAACI,QAAQ,GAAG,qCAAqC,EAAEG,IAAI,CAAC;AACrE,CAAC;AAED,OAAO,MAAM6H,oCAAoC,GAAI7H,IAAI,IAAK;EAC5D,OAAOP,IAAI,CAACI,QAAQ,GAAG,2CAA2C,EAAEG,IAAI,CAAC;AAC3E,CAAC;AAED,OAAO,MAAM8H,qCAAqC,GAAI9H,IAAI,IAAK;EAC7D,OAAOP,IAAI,CAACI,QAAQ,GAAG,qCAAqC,EAAEG,IAAI,CAAC;AACrE,CAAC;AAED,OAAO,MAAM+H,4BAA4B,GAAI/H,IAAI,IAAK;EACpD,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;AACvE,CAAC;;AAED;;AAEA,OAAO,MAAMgI,+BAA+B,GAAIhI,IAAI,IAAK;EACvD,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;AACvE,CAAC;AAED,OAAO,MAAMiI,sCAAsC,GAAIjI,IAAI,IAAK;EAC9D,OAAOP,IAAI,CAACI,QAAQ,GAAG,sCAAsC,EAAEG,IAAI,CAAC;AACtE,CAAC;AAED,OAAO,MAAMkI,sBAAsB,GAAIlI,IAAI,IAAK;EAC9C,OAAOR,GAAG,CACRK,QAAQ,GACR,cAAcG,IAAI,CAAC2F,QAAQ,eAAe3F,IAAI,CAACgD,aAAa,YAC9D,CAAC;AACH,CAAC;;AAID;;AAEA;AACA,OAAO,MAAMmF,oBAAoB,GAAInI,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,qBAAqB,EAAEG,IAAI,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAMoI,oBAAoB,GAAIpI,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,yBAAyB,EAAEG,IAAI,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMqI,uBAAuB,GAAGA,CAAChH,EAAE,EAAErB,IAAI,KAAK;EACnD;EACA,MAAMsI,UAAU,GAAG;IACjBjH,EAAE,EAAEA,EAAE;IACNkH,IAAI,EAAEvI,IAAI,CAACuI,IAAI;IACfC,WAAW,EAAExI,IAAI,CAACwI;EACpB,CAAC;EACD,OAAO9I,GAAG,CAACG,QAAQ,GAAG,uBAAuBwB,EAAE,EAAE,EAAEiH,UAAU,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAMG,6BAA6B,GAAGA,CAACpH,EAAE,EAAErB,IAAI,KAAK;EACzD,OAAON,GAAG,CAACG,QAAQ,GAAG,8BAA8BwB,EAAE,EAAE,EAAErB,IAAI,CAAC;AACjE,CAAC;;AAED;AACA,OAAO,MAAM0I,uBAAuB,GAAIrH,EAAE,IAAK;EAC7C,OAAO9B,MAAM,CAACM,QAAQ,GAAG,uBAAuBwB,EAAE,EAAE,CAAC;AACvD,CAAC;;AAID;;AAEA;AACA,OAAO,MAAMsH,OAAO,GAAI3I,IAAI,IAAK;EAC/B,OAAOP,IAAI,CAACI,QAAQ,GAAG,OAAO,EAAEG,IAAI,CAAC;AACvC,CAAC;;AAED;AACA,OAAO,MAAM4I,MAAM,GAAI5I,IAAI,IAAK;EAC9B,OAAOP,IAAI,CAACI,QAAQ,GAAG,WAAW,EAAEG,IAAI,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAM6I,SAAS,GAAGA,CAACxH,EAAE,EAAErB,IAAI,KAAK;EACrC,OAAON,GAAG,CAACG,QAAQ,GAAG,SAASwB,EAAE,EAAE,EAAErB,IAAI,CAAC;AAC5C,CAAC;;AAED;AACA,OAAO,MAAM8I,eAAe,GAAGA,CAACzH,EAAE,EAAErB,IAAI,KAAK;EAC3C,OAAON,GAAG,CAACG,QAAQ,GAAG,gBAAgBwB,EAAE,EAAE,EAAErB,IAAI,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAM+I,SAAS,GAAI1H,EAAE,IAAK;EAC/B,OAAO9B,MAAM,CAACM,QAAQ,GAAG,SAASwB,EAAE,EAAE,CAAC;AACzC,CAAC;;AAOD;;AAEA;AACA,OAAO,MAAM2H,WAAW,GAAIhJ,IAAI,IAAK;EACnC,OAAOP,IAAI,CAACI,QAAQ,GAAG,WAAW,EAAEG,IAAI,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMiJ,cAAc,GAAI5H,EAAE,IAAK;EACpC,OAAO7B,GAAG,CAACK,QAAQ,GAAG,aAAawB,EAAE,EAAE,CAAC;AAC1C,CAAC;;AAED;;AAEA;AACA,OAAO,MAAM6H,UAAU,GAAIlJ,IAAI,IAAK;EAClC,OAAOP,IAAI,CAACI,QAAQ,GAAG,UAAU,EAAEG,IAAI,CAAC;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMmJ,kBAAkB,GAAGA,CAAC9H,EAAE,EAAErB,IAAI,KAAK;EAC9C,OAAON,GAAG,CAACG,QAAQ,GAAG,YAAYwB,EAAE,WAAW,EAAErB,IAAI,CAAC;AACxD,CAAC;;AAED;;AAGA;AACA,OAAO,MAAMoJ,gBAAgB,GAAIhH,MAAM,IAAK;EAC1C,OAAO5C,GAAG,CAACK,QAAQ,GAAG,eAAe,EAAEuC,MAAM,CAAC;AAChD,CAAC;AAED,OAAO,MAAMiH,oBAAoB,GAAGA,CAAA,KAAM;EACxC,OAAO7J,GAAG,CAACK,QAAQ,GAAG,uBAAuB,CAAC;AAChD,CAAC;AACD;AACA,OAAO,MAAMyJ,mBAAmB,GAAItJ,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,eAAe,EAAEG,IAAI,CAAC;AAC/C,CAAC;AACD;AACA,OAAO,MAAMuJ,mBAAmB,GAAGA,CAACC,eAAe,EAAExJ,IAAI,KAAK;EAC5D,OAAON,GAAG,CAACG,QAAQ,GAAG,kBAAkB2J,eAAe,EAAE,EAAExJ,IAAI,CAAC;AAClE,CAAC;AACD;AACA,OAAO,MAAMyJ,kBAAkB,GAAID,eAAe,IAAK;EACrD,OAAOjK,MAAM,CAACM,QAAQ,GAAG,kBAAkB2J,eAAe,EAAE,CAAC;AAC/D,CAAC;AACD;AACA,OAAO,MAAME,iBAAiB,GAAI1J,IAAI,IAAK;EACzC,OAAOP,IAAI,CAACI,QAAQ,GAAG,qBAAqB,EAAEG,IAAI,CAAC;AACrD,CAAC;;AAKD;;AAEA,OAAO,MAAM2J,iBAAiB,GAAI3J,IAAI,IAAK;EACzC,OAAOR,GAAG,CACRK,QAAQ,GAAG,yBAAyBG,IAAI,CAACO,IAAI,UAAUP,IAAI,CAACQ,KAAK,EACnE,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMoJ,iBAAiB,GAAI5J,IAAI,IAAK;EACzC,MAAM6J,GAAG,GAAGhK,QAAQ,GAAG,qBAAqB;EAC5CyD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsG,GAAG,CAAC;EAClDvG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEvD,IAAI,CAAC;EACzC,OAAOL,WAAW,CAACkK,GAAG,EAAE7J,IAAI,CAAC;AAC/B,CAAC;;AAED;AACA,OAAO,MAAM8J,yBAAyB,GAAIC,cAAc,IAAK;EAC3D,OAAOxK,MAAM,CAACM,QAAQ,GAAG,yBAAyBkK,cAAc,EAAE,CAAC;AACrE,CAAC;AAGD,OAAO,MAAMC,eAAe,GAAIhK,IAAI,IAAK;EACvC,OAAOP,IAAI,CAACI,QAAQ,GAAG,mBAAmB,EAAEG,IAAI,CAAC;AACnD,CAAC;;AAID;;AAEA;;AAEA,OAAO,MAAMiK,WAAW,GAAIjK,IAAI,IAAK;EACnC,OAAOR,GAAG,CACRK,QAAQ,GACN,iBAAiBG,IAAI,CAACO,IAAI,UAAUP,IAAI,CAACQ,KAAK,WAAWR,IAAI,CAACkK,KAAK,WAAWlK,IAAI,CAACM,MAAM,EAC7F,CAAC;AACH,CAAC;AAED,OAAO,MAAM6J,UAAU,GAAG,MAAOzE,OAAO,IAAK;EAC3C,OAAOnG,MAAM,CAACM,QAAQ,GAAG,gBAAgB6F,OAAO,EAAE,CAAC;AACrD,CAAC;AAED,OAAO,MAAM0E,gBAAgB,GAAGA,CAAC1E,OAAO,EAAEjF,MAAM,KAAK;EACnD,OAAOf,GAAG,CAACG,QAAQ,GAAG,oBAAoB6F,OAAO,EAAE,EAAE;IAAEjF;EAAO,CAAC,CAAC;AAClE,CAAC;AAKD,OAAO,MAAM4J,eAAe,GAAIrK,IAAI,IAAK;EACvC,OAAOL,WAAW,CAACE,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMsK,oBAAoB,GAAGA,CAACtK,IAAI,EAAEuK,MAAM,KAAK;EACpD,OAAO3K,uBAAuB,CAACC,QAAQ,GAAG,gBAAgB0K,MAAM,EAAE,EAAEvK,IAAI,EAAE,KAAK,CAAC;AAClF,CAAC;;AAID;;AAEA;AACA,OAAO,MAAMwK,sBAAsB,GAAIxK,IAAI,IAAK;EAC9C,OAAOR,GAAG,CACRK,QAAQ,GACN,eAAeG,IAAI,CAACO,IAAI,UAAUP,IAAI,CAACQ,KAAK,WAAWR,IAAI,CAACkK,KAAK,WAAWlK,IAAI,CAACM,MAAM,EAC3F,CAAC;AACH,CAAC;AAGD,OAAO,MAAMmK,YAAY,GAAIC,OAAO,IAAK;EACvC,OAAOhL,GAAG,CAACG,QAAQ,GAAG,iBAAiB6K,OAAO,EAAE,CAAC;AACnD,CAAC;AACD,OAAO,MAAMC,YAAY,GAAID,OAAO,IAAK;EACvC,OAAOhL,GAAG,CAACG,QAAQ,GAAG,mBAAmB6K,OAAO,EAAE,CAAC;AACrD,CAAC;AAACE,GAAA,GAFWD,YAAY;AAGzB,OAAO,MAAME,UAAU,GAAG,MAAOH,OAAO,IAAK;EAC3C,OAAOnL,MAAM,CAACM,QAAQ,GAAG,UAAU6K,OAAO,EAAE,CAAC;AAC/C,CAAC;AAGD,OAAO,MAAMI,iBAAiB,GAAI9K,IAAI,IAAK;EACzC,OAAOP,IAAI,CAACI,QAAQ,GAAG,cAAc,EAAEG,IAAI,CAAC;AAC9C,CAAC;AAED,OAAO,MAAM+K,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAOvL,GAAG,CAACK,QAAQ,GAAG,kBAAkB,CAAC;AAC3C,CAAC;AAED,OAAO,MAAMmL,cAAc,GAAIC,MAAM,IAAK;EACxC,OAAOzL,GAAG,CAACK,QAAQ,GAAG,UAAUoL,MAAM,cAAc,CAAC;AACvD,CAAC;AAGD,OAAO,MAAMC,wBAAwB,GAAGA,CAACD,MAAM,EAAEjL,IAAI,KAAK;EACxD,OAAON,GAAG,CAACG,QAAQ,GAAG,UAAUoL,MAAM,EAAE,EAAEjL,IAAI,CAAC;AACjD,CAAC;;AAID;AACA,OAAO,MAAMmL,yBAAyB,GAAInL,IAAI,IAAK;EACjD,OAAOP,IAAI,CAACI,QAAQ,GAAG,+BAA+B,EAAEG,IAAI,CAAC;AAC/D,CAAC;;AAED;AACA,OAAO,MAAMoL,sBAAsB,GAAGA,CAAA,KAAM;EAC1C,OAAO5L,GAAG,CAACK,QAAQ,GAAG,6BAA6B,CAAC;AACtD,CAAC;;AAID;;AAEA;AACA,OAAO,MAAMwL,uDAAuD,GAAGA,CAAA,KAAM;EAC3E,OAAO7L,GAAG,CAACK,QAAQ,GAAG,4BAA4B,CAAC;AACrD,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMyL,mBAAmB,GAAItL,IAAI,IAAK;EAC3C,OAAOP,IAAI,CAACI,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AAC5D,CAAC;;AAED;AACA,OAAO,MAAMuL,kBAAkB,GAAIvL,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMwL,mBAAmB,GAAGA,CAACC,WAAW,EAAEzL,IAAI,KAAK;EACxD,OAAON,GAAG,CAACG,QAAQ,GAAG,8BAA8B4L,WAAW,EAAE,EAAEzL,IAAI,CAAC;AAC1E,CAAC;;AAED;AACA,OAAO,MAAM0L,mBAAmB,GAAID,WAAW,IAAK;EAClD,OAAOlM,MAAM,CAACM,QAAQ,GAAG,8BAA8B4L,WAAW,EAAE,CAAC;AACvE,CAAC;;AAED;AACA,OAAO,MAAME,kBAAkB,GAAI3L,IAAI,IAAK;EAC1C,OAAOP,IAAI,CAACI,QAAQ,GAAG,kCAAkC,EAAEG,IAAI,CAAC;AAClE,CAAC;;AAED;AACA,OAAO,MAAM4L,gBAAgB,GAAI5L,IAAI,IAAK;EACxC,OAAOP,IAAI,CAACI,QAAQ,GAAG,gCAAgC,EAAEG,IAAI,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAM6L,iBAAiB,GAAI7L,IAAI,IAAK;EACzC,OAAOP,IAAI,CAACI,QAAQ,GAAG,uCAAuC,EAAEG,IAAI,CAAC;AACvE,CAAC;;AAGD;AACA,OAAO,MAAM8L,oBAAoB,GAAI9L,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,yBAAyB,EAAEG,IAAI,CAAC;AACzD,CAAC;;AAGD;AACA,OAAO,MAAM+L,wBAAwB,GAAI/L,IAAI,IAAK;EAChD,OAAOP,IAAI,CAACI,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;AAC9D,CAAC;;AAED;AACA,OAAO,MAAMgM,wBAAwB,GAAIhM,IAAI,IAAK;EAChD,OAAOP,IAAI,CAACI,QAAQ,GAAG,8BAA8B,EAAEG,IAAI,CAAC;AAC9D,CAAC;;AAGD;;AAEA;AACA,OAAO,MAAMiM,eAAe,GAAIjM,IAAI,IAAK;EACvC,OAAOP,IAAI,CAACI,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAMkM,cAAc,GAAIlM,IAAI,IAAK;EACtC,OAAOP,IAAI,CAACI,QAAQ,GAAG,mBAAmB,EAAEG,IAAI,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMmM,cAAc,GAAInM,IAAI,IAAK;EACtC,OAAOP,IAAI,CAACI,QAAQ,GAAG,mBAAmB,EAAEG,IAAI,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMoM,YAAY,GAAIpM,IAAI,IAAK;EACpC,OAAOP,IAAI,CAACI,QAAQ,GAAG,iBAAiB,EAAEG,IAAI,CAAC;AACjD,CAAC;;AAED;AACA,OAAO,MAAMqM,eAAe,GAAIrM,IAAI,IAAK;EACvC,OAAOP,IAAI,CAACI,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AACpD,CAAC;AAID,OAAO,MAAMsM,eAAe,GAAItM,IAAI,IAAK;EACvC,OAAON,GAAG,CAACG,QAAQ,GAAG,oBAAoB,EAAEG,IAAI,CAAC;AACnD,CAAC;;AAKD;AACA,OAAO,MAAMuM,uBAAuB,GAAIvM,IAAI,IAAK;EAC/C,OAAOL,WAAW,CAACE,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AACnE,CAAC;AACD,OAAO,MAAMwM,qBAAqB,GAAIxM,IAAI,IAAK;EAC7C,OAAOP,IAAI,CAACI,QAAQ,GAAG,0BAA0B,EAAEG,IAAI,CAAC;AAC1D,CAAC;AACD,OAAO,MAAMyM,uBAAuB,GAAIzM,IAAI,IAAK;EAC/C,OAAOL,WAAW,CAACE,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AACnE,CAAC;AACD,OAAO,MAAM0M,uBAAuB,GAAI1M,IAAI,IAAK;EAC/C,OAAOP,IAAI,CAACI,QAAQ,GAAG,4BAA4B,EAAEG,IAAI,CAAC;AAC5D,CAAC;AACD,OAAO,MAAM2M,oBAAoB,GAAI3M,IAAI,IAAK;EAC5C,OAAOP,IAAI,CAACI,QAAQ,GAAG,yBAAyB,EAAEG,IAAI,CAAC;AACzD,CAAC;;AAID;AAAA,IAAA2C,EAAA,EAAAmC,GAAA,EAAA8F,GAAA;AAAAgC,YAAA,CAAAjK,EAAA;AAAAiK,YAAA,CAAA9H,GAAA;AAAA8H,YAAA,CAAAhC,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}