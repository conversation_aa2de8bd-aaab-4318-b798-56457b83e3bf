import {
  DELETE,
  GET,
  POST,
  PUT,
  UPLOAD_FILE,
  UPLOAD_FILE_WITH_METHOD,
} from "./apiController";

const endpoint = "/mainOrg";

// --------------------------------- Dashboard

// /get_dashboard_data
export const getDashboardData = () => {
  return GET(endpoint + `/get_dashboard_data`);
};

// /trainee_analytics
export const getTraineeAnalytics = (data) => {
  return POST(endpoint + `/trainee_analytics`, data);
};

// --------------------------------- Prifle

// ---------------------------------- Courses

// get all courses
export const courses_api = () => {
  return GET(endpoint + `/courses_api`);
};

// ---------------------------------- Course Analytics

// get course performance stats (enrollments, completed, in progress, etc.)
export const coursePerformance = (courseId) => {
  return GET(endpoint + `/course_stats/${courseId}`);
};

// get course performance details with pagination and filters
export const coursePerformanceDetails = (data) => {
  return GET(
    endpoint +
    `/course_user_details/${data.course_id}?search=${data.search}&page=${data.page}&limit=${data.limit}&status=${data.status}`
  );
};

// get course surveys
export const getCourseSurveys = (data) => {
  return POST(endpoint + `/course_surveys`, data);
};

// get course assessments
export const getCourseAssessments = (data) => {
  return POST(endpoint + `/course_assessments`, data);
};

// get assessment details
export const getAssessmentDetails = (assessmentId) => {
  return GET(endpoint + `/assessment_details/${assessmentId}`);
};

//get_assessment_analytics_using_gemini
export const getAssessmentAnalyticsUsingGemini = (data) => {
  return POST(endpoint + "/get_assessment_analytics_using_gemini", data);
};


// get single survey details
export const singleSurveyDetails = (data) => {
  return POST(endpoint + `/single_survey_details`, data);
};

// get course user activity
export const courseUserActivity = (courseId) => {
  return GET(endpoint + `/course_activity/${courseId}`);
};

// delete course
export const courseDelete = (data) => {
  return DELETE(endpoint + `/course/${data.course_id}`);
};

// send approval course
export const sendApprovalCourse = (data) => {
  return POST(endpoint + `/send_approval_course`, data);
};

// cancel approval course
export const cancelApprovalCourse = (data) => {
  return POST(endpoint + `/cancel_approval_course`, data);
};

// get course module
export const getCourseModule = (data) => {
  return GET(endpoint + `/modules/${data.id}?search=${data.search || ""}`);
};

// create module
export const createModule = (data) => {
  return POST(endpoint + `/module/new`, data);
};

// delete module
export const deleteModule = async (data) => {
  return DELETE(
    endpoint + `/course/${data.course_id}/module/${data.module_id}`
  );
};

// update module
export const updateModule = (moduleId, data) => {
  return PUT(endpoint + `/module/${moduleId}`, data);
};

// update module status
export const moduleStatus = (data) => {
  return PUT(endpoint + `/course/${data.course_id}/module/${data.module_id}`, {
    status: data.status,
  });
};

// get module content
export const getCourseModuleContent = (data) => {
  return GET(endpoint + `/modules-content/${data.module_id}`);
};

// create assessment
export const createAssessmentAPI = (data) => {
  return POST(endpoint + `/assessment`, data); // Calls the backend API to create an assessment
};

export const createDocument = (data) => {
  return UPLOAD_FILE(endpoint + `/document`, data);
};

export const createVideoAPI = (data) => {
  return UPLOAD_FILE(endpoint + `/video/upload`, data);
};

export const deleteContentById = async (data) => {
  return DELETE(endpoint + `/module-content/${data.content_id}/${data.type}`);
};

export const changeContentstatus = (params, data) => {
  return PUT(
    endpoint +
    `/modules-content-active-deactive/${params.module_id}/${params.content_id}`,
    data
  );
};

// update content order
export const updateContentOrder = (data) => {
  return POST(endpoint + `/content-order-update`, data);
};

export const getVideoDetailsById = (data) => {
  return GET(
    endpoint + `/get_video_details/${data.video_id}/${data.module_id}`
  );
};

export const getCourseReviews = (course_id, video_id) => {
  const queryParams = video_id
    ? `course_id=${course_id}&video_id=${video_id}`
    : `course_id=${course_id}`;
  return GET(`/org/get_course_reviews?${queryParams}`);
};

export const EditVideoContent = (data) => {
  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/edit-video`, data, "PUT");
};

export const editDocument = (data) => {
  return UPLOAD_FILE(endpoint + `/course/edit-document`, data); // Use POST method as defined in backend route
};

//   get_document/:doc_i
export const getDocumentById = (data) => {
  return GET(endpoint + `/get_document/${data.doc_id}`);
};

// get module questions
export const getModuleQuestions = (data) => {
  return GET(
    endpoint + `/course/get-questions/${data.module_id}/${data.assessment_id}`
  );
};

export const getCourseVideos = (data) => {
  const moduleParam = data.module_id ? `/${data.module_id}` : "";
  return GET(endpoint + `/course/videos/${data.course_id}${moduleParam}`);
};

export const addQuestion = async (data) => {
  try {
    // If data is FormData, use it directly; otherwise use POST
    let response;
    if (data instanceof FormData) {
      console.log("Using UploadFormData for form data");
      response = await UPLOAD_FILE(endpoint + `/course/add-questions`, data);
      console.log("Response from UploadFormData:", response);
    } else {
      console.log("Using POST for JSON data");
      response = await POST(endpoint + `/course/add-questions`, data);
      console.log("Response from POST:", response);
    }

    // If response is undefined, create a default error response
    if (!response) {
      console.warn(
        "Response from server is undefined, creating default response"
      );
      // Check if the form data was sent successfully despite no response
      return {
        success: true,
        message: "Question may have been added, but no response was received",
      };
    }

    // If we have a response with success property, it's already formatted correctly
    if (response.success !== undefined) {
      console.log(
        "Response already has success property, returning directly:",
        response
      );
      return response;
    }

    // If we get here, we have a response but it's not in the expected format
    console.log("Formatting response to expected format:", response);
    return {
      success: true,
      message: "Question added successfully",
      data: response,
    };
  } catch (error) {
    console.error("Error in addQuestion:", error);
    // Return an error object rather than throwing
    return { success: false, error: error.toString() };
  }
};

export const deleteQuestion = (data) => {
  return DELETE(endpoint + `/course/delete-question/${data.question_id}`);
};

export const editQuestion = (data) => {
  // If data is FormData, use it directly with PUT method; otherwise use PUT
  if (data instanceof FormData) {
    const questionId = data.get("id");
    return UPLOAD_FILE_WITH_METHOD(
      endpoint + `/course/edit-question/${questionId}`,
      data,
      "PUT"
    );
  } else {
    const questionId = data.id;
    return PUT(endpoint + `/course/edit-question/${questionId}`, data);
  }
};

export const editAssessment = (data) => {
  return PUT(endpoint + `/edit-assessment`, data);
};

// create course
export const getCourseCertificate = (search = "") => {
  return GET(
    endpoint + `/get_course_certificate${search ? `?search=${search}` : ""}`
  );
};

export const createNewCourse = (data) => {
  return UPLOAD_FILE_WITH_METHOD(endpoint + `/course/new`, data);
};

//   /course_details_by_id uign payload
export const getCourseDetailsById = (data) => {
  return POST(endpoint + `/course_details_by_id`, data);
};

export const updateCourseDetails = (courseId, data) => {
  return PUT(endpoint + `/course/update/${courseId}`, data);
};

export const getCourseList = (data) => {
  return GET(
    endpoint +
    `/courses?page=${data.page}&limit=${data.limit}&search=${data.search || ""
    }&status=${data.status || "all"}`
  );
};

// ---------------------------------------------------- course ApprovalRequest

// /get_course_categories
export const getCourseApprovalANDApprovedANDRejected = () => {
  return GET(endpoint + `/course_approval_and_approved_and_rejected`);
};

export const approveCourse = (data) => {
  return POST(endpoint + `/approve_course/${data.id}`);
};

export const RejectApprovalCourse = (data) => {
  return POST(endpoint + `/course/reject_course`, {
    course_id: data.id,
    rejected_note: data.reason,
  });
};

//   ------------------------------------------------------------ classroom

export const classroomDashboard = (data) => {
  return POST(endpoint + `/classroom/dashboard`, data);
};

// /classroom/visibility
export const classroomVisibility = (data) => {
  return POST(endpoint + `/classroom/visibility`, data);
};

// --Get classroom data api
export const getClassroomDetails = (data) => {
  return POST(endpoint + `/classroom/get_classes`, data);
};

export const getAllTrainers = (data) => {
  return GET(endpoint + `/classroom/get_trainers`);
};

export const createClassroom = (data) => {
  return UPLOAD_FILE(endpoint + `/classroom`, data);
};

// --Update classroom data api
export const updateClassroomDetails = (data) => {
  return PUT(endpoint + `/classroom`, data);
};

// -- Update classroom status api
export const updateClassroomStatus = (data) => {
  return PUT(endpoint + `/classroom/status`, data);
};

// -- Delete classroom api
export const deleteClassroom = (data) => {
  return DELETE(endpoint + `/classroom/${data.classroom_id}`);
};

// -----------------------------------------------  classroom Trainees

//-- get trainees details api
export const getTrainees = (data) => {
  return POST(endpoint + `/classroom/trainee`, data);
};

export const removeTraineeFromClassroom = ({ user_id, class_id }) => {
  return DELETE(
    endpoint + `/classroom/trainee?user_id=${user_id}&class_id=${class_id}`
  );
};

// -- get all user from organization
export const getAllUsersExceptClassroom = (data) => {
  return POST(endpoint + `/classroom/get_all_trainees`, data);
};

//--- Add multiple trainees to classroom
export const addTraineesToClassroom = (data) => {
  return POST(endpoint + `/classroom/trainee/add`, data);
};

//--- Copy assesss to one class to another
export const deleteMultipleTrainees = (data) => {
  return POST(endpoint + `/classroom/trainee/delete`, data);
};

//   -------------------------------------------- classroom assessments

export const getAssignmentsForClassroom = (data) => {
  return POST(endpoint + "/classroom/get_assignments", data);
};

export const addClassroomAssignment = (data) => {
  // If data is FormData (contains file), use UploadFormData, otherwise use regular POST
  if (data instanceof FormData) {
    return UPLOAD_FILE(endpoint + `/classroom/create_assignment`, data);
  } else {
    return POST(endpoint + `/classroom/create_assignment`, data);
  }
};

export const deleteClassroomAssignment = (data) => {
  return POST(endpoint + `/classroom/delete_assignment`, data);
};

export const updateClassroomAssignment = (data) => {
  // If data is FormData (contains file), use UploadFormData with PUT, otherwise use regular PUT
  if (data instanceof FormData) {
    return UPLOAD_FILE(endpoint + `/classroom/update_assignment`, data, "PUT");
  } else {
    return PUT(endpoint + `/classroom/update_assignment`, data);
  }
};

// ------------------------------------------------- classroom assignment details

export const getClassroomAssignmentQuestions = (data) => {
  return POST(endpoint + `/classroom/get_assignment_questions`, data);
};

export const addClassroomAssignmentQuestion = (data) => {
  // for (let pair of data.entries()) {
  //   console.log('addClassroomAssignmentQuestion',pair[0] + ': ', pair[1]);
  // }

  // Check if data is FormData and if response_type is text
  if (data instanceof FormData) {
    // Get response_type from FormData
    const questionType = data.get("question_type");
    // console.log('responseType--------------------------------------',responseType)

    if (questionType === "text") {
      // console.log('Using POST for text response type')
      return POST(endpoint + `/classroom/add_assignment_question`, data);
    } else {
      console.log("Using UploadFormData for non-text response type");
      return UPLOAD_FILE(endpoint + `/classroom/add_assignment_question`, data);
    }
  } else {
    // console.log('Using POST for non-FormData')
    return POST(endpoint + `/classroom/add_assignment_question`, data);
  }
};

export const updateClassroomAssignmentQuestion = (data) => {
  for (let pair of data.entries()) {
    console.log(
      "------------------------------------------",
      pair[0] + ": ",
      pair[1]
    );
  }

  if (data instanceof FormData) {
    // Get response_type from FormData
    const questionType = data.get("question_type");
    const responseType = data.get("response_type");
    console.log(
      "questionType--------------------------------------",
      questionType
    );
    console.log(
      "responseType --------------------------------------",
      responseType
    );

    if (questionType === "text") {
      // console.log('Using POST for text response type')
      return POST(endpoint + `/classroom/update_assignment_question`, data);
    } else {
      // console.log('Using UploadFormData for non-text response type')
      return UPLOAD_FILE(
        endpoint + `/classroom/update_assignment_question`,
        data
      );
    }
  } else {
    // console.log('Using PUT for non-FormData')
    return POST(endpoint + `/classroom/update_assignment_question`, data);
  }
};
export const deleteClassroomAssignmentQuestion = (data) => {
  return POST(endpoint + `/classroom/delete_assignment_question`, data);
};

export const getClassroomAssignmentSubmissions = (data) => {
  return POST(endpoint + `/classroom/get_assignment_submissions`, data);
};

export const gradeClassroomAssignmentSubmission = (data) => {
  return POST(endpoint + `/classroom/grade_assignment`, data);
};

export const getClassroomAssignmentDetails = (data) => {
  return POST(endpoint + `/classroom/get_class_assignment`, data);
};

export const userAssignmentStatus = (data) => {
  return POST(endpoint + `/classroom/user_assignment_status`, data);
};

//   ------------------------------------------------------------------- classroom assessment

// // --Get classroom data api
// export const getClassroomDetails = (data) => {
//     return POST(endpoint + `/classroom/get_classes`, data);
//   };

// -- Get All Assessment to clssroom
export const getAllAssessmentToClassroom = (data) => {
  return POST(endpoint + `/classroom/assessments/all`, data);
};

// add assessment to classroom
export const addAssessmentToClassroom = (data) => {
  return POST(endpoint + `/classroom/assessment/add`, data);
};

// add assessment to classroom
export const removeAssessmentFromClassroom = (data) => {
  return POST(endpoint + `/classroom/assessment`, data);
};

// update assessment
export const updateAssessmentInClassroom = (data) => {
  return PUT(endpoint + `/classroom/assessment`, data);
};

//--- Copy assesss to one class to another
export const copyAssessment = (data) => {
  return POST(endpoint + `/classroom/assessment/copy`, data);
};

// -------------------------------------------------------------- question bank

// Question Bank
export const getQuestionBank = (data) => {
  // Ensure all required parameters are included
  const payload = {
    page: data.page || 1,
    limit: data.limit || 10,
    search: data.search || "",
  };
  return POST(endpoint + "/question_bank/questions", payload);
};

export const createQuestionBank = (data) => {
  return POST(endpoint + "/question_bank/add_question", data);
};

export const updateQuestionBank = (data) => {
  return PUT(endpoint + "/question_bank/update_question", data);
};

export const deleteQuestionBank = (data) => {
  return POST(endpoint + "/question_bank/delete_question", data);
};

export const getQuestionBankById = (data) => {
  return POST(endpoint + "/question_bank/get_question_by_id", data);
};

export const getTags = () => {
  return GET(endpoint + "/question_tags");
};

// /delete_question_tag
export const deleteQuestionTag = (data) => {
  return POST(endpoint + "/delete_question_tag", data);
};

// ------------------------------------------------------------------ asssesment details

export const addQuestionsToAssessment = (data) => {
  return POST(endpoint + `/classroom/assessment/add_questions`, data);
};

export const getQuestionsInAssessmentForAddModule = (data) => {
  return POST(endpoint + `/classroom/assessment/available-questions`, data);
};

export const getAllQuestionsForClassroomAssessment = (data) => {
  return POST(endpoint + `/classroom/assessment/questions/get`, data);
};

export const removeQuestionFromAssessment = (data) => {
  return POST(endpoint + `/classroom/assessment/remove_question`, data);
};

// --------------------- classroom assessment analytics

export const getAssessmentResultsInClassroom = (data) => {
  return POST(endpoint + `/classroom/assessment/trainee-results`, data);
};

export const getSpecificAssessmentResultInClassroom = (data) => {
  return POST(endpoint + `/classroom/assessment/trainee-result`, data);
};

export const getAssessmentAnalytics = (data) => {
  return GET(
    endpoint +
    `/classroom/${data.class_id}/assessment/${data.assessment_id}/analytics`
  );
};



// -------------------------------------------------------- Settings About Organization

// Get organization information with pagination and search
export const getAboutOrganization = (data) => {
  return POST(endpoint + `/about-organization`, data);
};

// Add new organization information
export const addAboutOrganization = (data) => {
  return POST(endpoint + `/about-organization/add`, data);
};

// Update organization information
export const updateAboutOrganization = (id, data) => {
  // Ensure id is included in both URL and body
  const updateData = {
    id: id,
    name: data.name,
    description: data.description
  };
  return PUT(endpoint + `/about-organization/${id}`, updateData);
};

// Update organization information status (active/inactive)
export const updateAboutOrganizationStatus = (id, data) => {
  return PUT(endpoint + `/about-organization/status/${id}`, data);
};

// Delete organization information (soft delete)
export const deleteAboutOrganization = (id) => {
  return DELETE(endpoint + `/about-organization/${id}`);
};



// ------------------------------------------------------------- setting FAQ

// Get FAQs with pagination and search
export const getFAQs = (data) => {
  return POST(endpoint + `/faqs`, data);
};

// Add new FAQ
export const addFAQ = (data) => {
  return POST(endpoint + `/faqs/add`, data);
};

// Update FAQ
export const updateFAQ = (id, data) => {
  return PUT(endpoint + `/faqs/${id}`, data);
};

// Update FAQ status (active/inactive)
export const updateFAQStatus = (id, data) => {
  return PUT(endpoint + `/faqs/status/${id}`, data);
};

// Delete FAQ (soft delete)
export const deleteFAQ = (id) => {
  return DELETE(endpoint + `/faqs/${id}`);
};






// ------------------- setting payment

// Get payments with pagination, search and status filter
export const getPayments = (data) => {
  return POST(endpoint + `/payments`, data);
};

// Get payment details by ID
export const getPaymentById = (id) => {
  return GET(endpoint + `/payments/${id}`);
};

// ------------------- setting tickets

// Get tickets with pagination, search and status filter
export const getTickets = (data) => {
  return POST(endpoint + `/tickets`, data);
};

// Send ticket response and update status
export const sendTicketResponse = (id, data) => {
  return PUT(endpoint + `/tickets/${id}/response`, data);
};

// -------------------------------------------- Announcement 


// Create a API endpoint for get Announcements endpoint
export const getAnnouncements = (params) => {
  return GET(endpoint + `/announcement`, params);
};

export const getAnnouncementsRole = () => {
  return GET(endpoint + `/get_announcment_role`);
};
// create a API endpoint for creating announcements
export const createAnnouncements = (data) => {
  return POST(endpoint + `/announcement`, data);
};
// create a API endpoint for updating announcements
export const updateAnnouncements = (announcement_id, data) => {
  return PUT(endpoint + `/announcements/${announcement_id}`, data);
};
// create a API endpoint for deleting announcements
export const deleteAnnouncement = (announcement_id) => {
  return DELETE(endpoint + `/announcements/${announcement_id}`);
};
// Send Announcements through firebase
export const sendAnnouncements = (data) => {
  return POST(endpoint + `/send_announcements`, data);
};




// ------------------------------------------------------- certificates 

export const getOrgCertificate = (data) => {
  return GET(
    endpoint + `/get_certificate?page=${data.page}&limit=${data.limit}`
  );
};

// create a API endpoint for creating certificates
export const createCertificate = (data) => {
  const url = endpoint + `/create_certificate`;
  console.log('Creating certificate with URL:', url);
  console.log('FormData being sent:', data);
  return UPLOAD_FILE(url, data);
};

// create a API endpoint for deleting certificates
export const deleteCertificateTemplate = (certificate_id) => {
  return DELETE(endpoint + `/certificate_template/${certificate_id}`);
};


export const editCertificate = (data) => {
  return POST(endpoint + `/edit_certificate`, data);
};



// ------------------------------------------------------------- roles and access

// ---------------------- Users 

export const getOrgUsers = (data) => {
  return GET(
    endpoint +
      `/orgUser?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`
  );
};

export const deleteUser = async (user_id) => {
  return DELETE(endpoint + `/delete_user/${user_id}`);
};

export const toggleUserStatus = (user_id, status) => {
  return PUT(endpoint + `/deactivate_user/${user_id}`, { status });
};




export const createAdminUser = (data) => {
  return UPLOAD_FILE(endpoint + `/create-admin-user`, data);
};

export const editOrganisationUser = (data, userId) => {
  return UPLOAD_FILE_WITH_METHOD(endpoint + `/editOrgUser/${userId}`, data, 'PUT');
};



// --------------------------------------- roles and permissions 

// Roles and Permissions
export const getRolesAndPermissions = (data) => {
  return GET(
    endpoint +
      `/roles?page=${data.page}&limit=${data.limit}&status=${data.state}&search=${data.search}`
  );
};


export const activateRole = (role_id) => {
  return PUT(endpoint + `/roles/active/${role_id}`);
};
export const InactiveRole = (role_id) => {
  return PUT(endpoint + `/roles/deactive/${role_id}`);
};
export const deleteRole = async (role_id) => {
  return DELETE(endpoint + `/roles/${role_id}`);
};


export const createPermissions = (data) => {
  return POST(endpoint + `/create_role`, data);
};

export const getPermissions = () => {
  return GET(endpoint + `/get_permissions`);
};

export const getRoleDetails = (roleId) => {
  return GET(endpoint + `/roles/${roleId}/permissions`);
};


export const updateRoleAndPermissions = (roleId, data) => {
  return PUT(endpoint + `/roles/${roleId}`, data);
};



// /create_full_module_hierarchy
export const createFullModuleHierarchy = (data) => {
  return POST(endpoint + `/create_full_module_hierarchy`, data);
};

// /get_permissions_by_role_id
export const getPermissionsByRoleId = () => {
  return GET(endpoint + `/get_permissions_by_role_id`);
};



// -------new 

// /get_full_module_hierarchy
export const getFullModulegetFullModuleHierarchyWithActionsHierarchy = () => {
  return GET(endpoint + `/get_full_module_hierarchy`);
};

// --------------------------------- Zoom Meeting SDK Live Classes

// Create a new live class with Zoom meeting
export const createZoomLiveClass = (data) => {
  return POST(endpoint + `/classroom/zoom_live_class`, data);
};

// Get all live classes for a classroom
export const getZoomLiveClasses = (data) => {
  return POST(endpoint + `/classroom/zoom_live_classes`, data);
};

// Update a live class
export const updateZoomLiveClass = (liveClassId, data) => {
  return PUT(endpoint + `/classroom/zoom_live_class/${liveClassId}`, data);
};

// Delete a live class
export const deleteZoomLiveClass = (liveClassId) => {
  return DELETE(endpoint + `/classroom/zoom_live_class/${liveClassId}`);
};

// Start a live class meeting
export const startZoomLiveClass = (data) => {
  return POST(endpoint + `/classroom/zoom_live_class/start`, data);
};

// End a live class meeting
export const endZoomLiveClass = (data) => {
  return POST(endpoint + `/classroom/zoom_live_class/end`, data);
};

// Generate join token for trainee
export const generateJoinToken = (data) => {
  return POST(endpoint + `/classroom/zoom_live_class/join_token`, data);
};


// updateRolePermission 
export const updateRolePermission = (data) => {
  return POST(endpoint + `/update_role_permission`, data);
};


//insertRoleAndPermissions
export const insertRoleAndPermissions = (data) => {
  return POST(endpoint + `/insert_role_and_permissions`, data);
};

// deleteRoleAndPermissions
export const deleteRoleAndPermissions = (data) => {
  return POST(endpoint + `/delete_role_and_permissions`, data);
};


// ------------------------------ live_Classroom 

// /create_live_class
export const createLiveClass = (data) => {
  return POST(endpoint + `/create_live_class`, data);
};

// Get all live classes
export const getLiveClasses = (data) => {
  return POST(endpoint + `/get_live_classes`, data);
};

// Start live class
export const startLiveClass = (data) => {
  return POST(endpoint + `/start_live_class`, data);
};

// End live class
export const endLiveClass = (data) => {
  return POST(endpoint + `/end_live_class`, data);
};

// Delete live class
export const deleteLiveClass = (data) => {
  return POST(endpoint + `/delete_live_class`, data);
};



export const updateLiveClass = (data) => {
  return PUT(endpoint + `/update-live-class`, data);
};




// add recourses  
export const createClassroomResource = (data) => {
  return UPLOAD_FILE(endpoint + `/create_classroom_resource`, data);
};
export const getClassroomResources = (data) => {
  return POST(endpoint + `/get_classroom_resources`, data);
};
export const updateClassroomResource = (data) => {
  return UPLOAD_FILE(endpoint + `/update_classroom_resource`, data);
};
export const deleteClassroomResource = (data) => {
  return POST(endpoint + `/delete_classroom_resource`, data);
};
export const toggleResourceStatus = (data) => {
  return POST(endpoint + `/toggle_resource_status`, data);
};



// -------------------------------------------------------------------------- organization 



